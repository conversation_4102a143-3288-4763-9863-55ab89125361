@page "/database-maintenance"
@using System.Text.Json
@inject HttpClient Http
@inject NotificationService NotificationService
@inject DialogService DialogService

<PageTitle>صيانة قاعدة البيانات</PageTitle>

<RadzenStack Gap="1rem">
    <RadzenRow>
        <RadzenColumn Size="12">
            <RadzenText TextStyle="TextStyle.H3" TagName="TagName.H1" class="rz-pt-8">
                <RadzenIcon Icon="storage" class="rz-me-1" />
                صيانة قاعدة البيانات
            </RadzenText>
        </RadzenColumn>
    </RadzenRow>

    <!-- Database Health Status -->
    <RadzenCard class="rz-p-4">
        <RadzenText TextStyle="TextStyle.H6" class="rz-mb-3">حالة قاعدة البيانات</RadzenText>
        
        @if (healthStatus != null)
        {
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenStack Gap="1rem">
                        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                            <RadzenIcon Icon="@(healthStatus.IsHealthy ? "check_circle" : "error")" 
                                Style="@($"color: {(healthStatus.IsHealthy ? "var(--rz-success)" : "var(--rz-danger)")}")" />
                            <RadzenText TextStyle="TextStyle.H6">
                                @(healthStatus.IsHealthy ? "قاعدة البيانات تعمل بشكل طبيعي" : "توجد مشاكل في قاعدة البيانات")
                            </RadzenText>
                        </RadzenStack>
                        
                        <RadzenText TextStyle="TextStyle.Subtitle2">حجم قاعدة البيانات: @healthStatus.DatabaseSize</RadzenText>
                        <RadzenText TextStyle="TextStyle.Subtitle2">عدد الجداول: @healthStatus.TablesCount</RadzenText>
                        <RadzenText TextStyle="TextStyle.Subtitle2">آخر فحص: @healthStatus.LastCheckDate.ToString("yyyy-MM-dd HH:mm")</RadzenText>
                    </RadzenStack>
                </RadzenColumn>
                
                <RadzenColumn Size="12" SizeMD="6">
                    @if (healthStatus.Issues?.Any() == true)
                    {
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">المشاكل المكتشفة:</RadzenText>
                        <RadzenStack Gap="0.5rem">
                            @foreach (var issue in healthStatus.Issues)
                            {
                                <RadzenAlert AlertStyle="AlertStyle.Warning" ShowIcon="true">
                                    @issue
                                </RadzenAlert>
                            }
                        </RadzenStack>
                    }
                    else
                    {
                        <RadzenAlert AlertStyle="AlertStyle.Success" ShowIcon="true">
                            لا توجد مشاكل مكتشفة
                        </RadzenAlert>
                    }
                </RadzenColumn>
            </RadzenRow>
        }
        else
        {
            <RadzenProgressBarCircular ShowValue="false" Mode="ProgressBarMode.Indeterminate" />
            <RadzenText TextStyle="TextStyle.Body1" class="rz-mt-3">جاري فحص حالة قاعدة البيانات...</RadzenText>
        }
    </RadzenCard>

    <!-- Maintenance Actions -->
    <RadzenCard class="rz-p-4">
        <RadzenText TextStyle="TextStyle.H6" class="rz-mb-3">إجراءات الصيانة</RadzenText>
        
        <RadzenRow Gap="1rem">
            <RadzenColumn Size="12" SizeMD="6">
                <RadzenCard class="rz-p-3" Style="border: 1px solid var(--rz-border-color);">
                    <RadzenStack Gap="1rem">
                        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                            <RadzenIcon Icon="update" Style="color: var(--rz-primary);" />
                            <RadzenText TextStyle="TextStyle.Subtitle1">تطبيق الترحيلات</RadzenText>
                        </RadzenStack>
                        
                        <RadzenText TextStyle="TextStyle.Body2">
                            تطبيق آخر تحديثات هيكل قاعدة البيانات والترحيلات المعلقة
                        </RadzenText>
                        
                        <RadzenButton Text="تطبيق الترحيلات" Icon="update" ButtonStyle="ButtonStyle.Primary" 
                            Click="@ApplyMigrations" IsBusy="@isApplyingMigrations" />
                    </RadzenStack>
                </RadzenCard>
            </RadzenColumn>
            
            <RadzenColumn Size="12" SizeMD="6">
                <RadzenCard class="rz-p-3" Style="border: 1px solid var(--rz-border-color);">
                    <RadzenStack Gap="1rem">
                        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                            <RadzenIcon Icon="tune" Style="color: var(--rz-success);" />
                            <RadzenText TextStyle="TextStyle.Subtitle1">تحسين قاعدة البيانات</RadzenText>
                        </RadzenStack>
                        
                        <RadzenText TextStyle="TextStyle.Body2">
                            تحسين أداء قاعدة البيانات وإعادة بناء الفهارس
                        </RadzenText>
                        
                        <RadzenButton Text="تحسين قاعدة البيانات" Icon="tune" ButtonStyle="ButtonStyle.Success" 
                            Click="@OptimizeDatabase" IsBusy="@isOptimizing" />
                    </RadzenStack>
                </RadzenCard>
            </RadzenColumn>
            
            <RadzenColumn Size="12" SizeMD="6">
                <RadzenCard class="rz-p-3" Style="border: 1px solid var(--rz-border-color);">
                    <RadzenStack Gap="1rem">
                        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                            <RadzenIcon Icon="build" Style="color: var(--rz-warning);" />
                            <RadzenText TextStyle="TextStyle.Subtitle1">إصلاح قاعدة البيانات</RadzenText>
                        </RadzenStack>
                        
                        <RadzenText TextStyle="TextStyle.Body2">
                            إصلاح المشاكل المكتشفة في قاعدة البيانات
                        </RadzenText>
                        
                        <RadzenButton Text="إصلاح قاعدة البيانات" Icon="build" ButtonStyle="ButtonStyle.Warning" 
                            Click="@RepairDatabase" IsBusy="@isRepairing" />
                    </RadzenStack>
                </RadzenCard>
            </RadzenColumn>
            
            <RadzenColumn Size="12" SizeMD="6">
                <RadzenCard class="rz-p-3" Style="border: 1px solid var(--rz-border-color);">
                    <RadzenStack Gap="1rem">
                        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                            <RadzenIcon Icon="refresh" Style="color: var(--rz-info);" />
                            <RadzenText TextStyle="TextStyle.Subtitle1">فحص الحالة</RadzenText>
                        </RadzenStack>
                        
                        <RadzenText TextStyle="TextStyle.Body2">
                            إجراء فحص شامل لحالة قاعدة البيانات
                        </RadzenText>
                        
                        <RadzenButton Text="فحص الحالة" Icon="refresh" ButtonStyle="ButtonStyle.Info" 
                            Click="@CheckHealth" IsBusy="@isCheckingHealth" />
                    </RadzenStack>
                </RadzenCard>
            </RadzenColumn>
        </RadzenRow>
    </RadzenCard>

    <!-- Database Information -->
    @if (databaseInfo != null)
    {
        <RadzenCard class="rz-p-4">
            <RadzenText TextStyle="TextStyle.H6" class="rz-mb-3">معلومات قاعدة البيانات</RadzenText>
            
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenStack Gap="1rem">
                        <RadzenText TextStyle="TextStyle.Subtitle2">اسم قاعدة البيانات: @databaseInfo.DatabaseName</RadzenText>
                        <RadzenText TextStyle="TextStyle.Subtitle2">نوع قاعدة البيانات: @databaseInfo.DatabaseType</RadzenText>
                        <RadzenText TextStyle="TextStyle.Subtitle2">إصدار الخادم: @databaseInfo.ServerVersion</RadzenText>
                        <RadzenText TextStyle="TextStyle.Subtitle2">حجم البيانات: @databaseInfo.DataSize</RadzenText>
                    </RadzenStack>
                </RadzenColumn>
                
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenStack Gap="1rem">
                        <RadzenText TextStyle="TextStyle.Subtitle2">حجم الفهارس: @databaseInfo.IndexSize</RadzenText>
                        <RadzenText TextStyle="TextStyle.Subtitle2">المساحة المستخدمة: @databaseInfo.UsedSpace</RadzenText>
                        <RadzenText TextStyle="TextStyle.Subtitle2">المساحة المتاحة: @databaseInfo.FreeSpace</RadzenText>
                        <RadzenText TextStyle="TextStyle.Subtitle2">آخر نسخة احتياطية: @(databaseInfo.LastBackupDate?.ToString("yyyy-MM-dd HH:mm") ?? "لا توجد")</RadzenText>
                    </RadzenStack>
                </RadzenColumn>
            </RadzenRow>
        </RadzenCard>
    }

    <!-- Maintenance Log -->
    @if (maintenanceLog?.Any() == true)
    {
        <RadzenCard class="rz-p-4">
            <RadzenText TextStyle="TextStyle.H6" class="rz-mb-3">سجل الصيانة</RadzenText>
            
            <RadzenDataGrid Data="@maintenanceLog" TItem="MaintenanceLogEntry" 
                AllowPaging="true" PageSize="10" AllowSorting="true">
                
                <Columns>
                    <RadzenDataGridColumn TItem="MaintenanceLogEntry" Property="Date" Title="التاريخ" Width="150px">
                        <Template Context="entry">
                            @entry.Date.ToString("yyyy-MM-dd HH:mm")
                        </Template>
                    </RadzenDataGridColumn>
                    
                    <RadzenDataGridColumn TItem="MaintenanceLogEntry" Property="Action" Title="الإجراء" Width="200px" />
                    
                    <RadzenDataGridColumn TItem="MaintenanceLogEntry" Property="Status" Title="الحالة" Width="100px">
                        <Template Context="entry">
                            @if (entry.Status == "Success")
                            {
                                <RadzenBadge BadgeStyle="BadgeStyle.Success" Text="نجح" />
                            }
                            else
                            {
                                <RadzenBadge BadgeStyle="BadgeStyle.Danger" Text="فشل" />
                            }
                        </Template>
                    </RadzenDataGridColumn>
                    
                    <RadzenDataGridColumn TItem="MaintenanceLogEntry" Property="Duration" Title="المدة" Width="100px" />
                    
                    <RadzenDataGridColumn TItem="MaintenanceLogEntry" Property="Details" Title="التفاصيل" />
                </Columns>
            </RadzenDataGrid>
        </RadzenCard>
    }
</RadzenStack>

@code {
    private DatabaseHealthStatus healthStatus;
    private DatabaseInfo databaseInfo;
    private List<MaintenanceLogEntry> maintenanceLog = new();
    
    private bool isApplyingMigrations = false;
    private bool isOptimizing = false;
    private bool isRepairing = false;
    private bool isCheckingHealth = false;

    protected override async Task OnInitializedAsync()
    {
        await CheckHealth();
        await LoadDatabaseInfo();
        await LoadMaintenanceLog();
    }

    private async Task CheckHealth()
    {
        isCheckingHealth = true;
        try
        {
            var response = await Http.GetAsync("api/DatabaseMaintenance/health-check");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                healthStatus = JsonSerializer.Deserialize<DatabaseHealthStatus>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Success,
                    Summary = "نجح",
                    Detail = "تم فحص حالة قاعدة البيانات بنجاح"
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = "فشل في فحص حالة قاعدة البيانات"
            });
        }
        finally
        {
            isCheckingHealth = false;
        }
    }

    private async Task ApplyMigrations()
    {
        var confirm = await DialogService.Confirm("هل أنت متأكد من تطبيق الترحيلات؟ قد يستغرق هذا بعض الوقت.", "تأكيد تطبيق الترحيلات", 
            new ConfirmOptions() { OkButtonText = "نعم", CancelButtonText = "إلغاء" });

        if (confirm == true)
        {
            isApplyingMigrations = true;
            try
            {
                var response = await Http.PostAsync("api/DatabaseMaintenance/apply-migrations", null);
                if (response.IsSuccessStatusCode)
                {
                    NotificationService.Notify(new NotificationMessage
                    {
                        Severity = NotificationSeverity.Success,
                        Summary = "نجح",
                        Detail = "تم تطبيق الترحيلات بنجاح"
                    });
                    await CheckHealth();
                    await LoadMaintenanceLog();
                }
            }
            catch (Exception ex)
            {
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Error,
                    Summary = "خطأ",
                    Detail = "فشل في تطبيق الترحيلات"
                });
            }
            finally
            {
                isApplyingMigrations = false;
            }
        }
    }

    private async Task OptimizeDatabase()
    {
        var confirm = await DialogService.Confirm("هل أنت متأكد من تحسين قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.", "تأكيد التحسين", 
            new ConfirmOptions() { OkButtonText = "نعم", CancelButtonText = "إلغاء" });

        if (confirm == true)
        {
            isOptimizing = true;
            try
            {
                var response = await Http.PostAsync("api/DatabaseMaintenance/optimize-database", null);
                if (response.IsSuccessStatusCode)
                {
                    NotificationService.Notify(new NotificationMessage
                    {
                        Severity = NotificationSeverity.Success,
                        Summary = "نجح",
                        Detail = "تم تحسين قاعدة البيانات بنجاح"
                    });
                    await CheckHealth();
                    await LoadMaintenanceLog();
                }
            }
            catch (Exception ex)
            {
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Error,
                    Summary = "خطأ",
                    Detail = "فشل في تحسين قاعدة البيانات"
                });
            }
            finally
            {
                isOptimizing = false;
            }
        }
    }

    private async Task RepairDatabase()
    {
        var confirm = await DialogService.Confirm("هل أنت متأكد من إصلاح قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.", "تأكيد الإصلاح", 
            new ConfirmOptions() { OkButtonText = "نعم", CancelButtonText = "إلغاء" });

        if (confirm == true)
        {
            isRepairing = true;
            try
            {
                var response = await Http.PostAsync("api/DatabaseMaintenance/repair-database", null);
                if (response.IsSuccessStatusCode)
                {
                    NotificationService.Notify(new NotificationMessage
                    {
                        Severity = NotificationSeverity.Success,
                        Summary = "نجح",
                        Detail = "تم إصلاح قاعدة البيانات بنجاح"
                    });
                    await CheckHealth();
                    await LoadMaintenanceLog();
                }
            }
            catch (Exception ex)
            {
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Error,
                    Summary = "خطأ",
                    Detail = "فشل في إصلاح قاعدة البيانات"
                });
            }
            finally
            {
                isRepairing = false;
            }
        }
    }

    private async Task LoadDatabaseInfo()
    {
        try
        {
            var response = await Http.GetAsync("api/DatabaseMaintenance/database-size");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                databaseInfo = JsonSerializer.Deserialize<DatabaseInfo>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
        }
        catch (Exception ex)
        {
            // Database info loading is optional
        }
    }

    private async Task LoadMaintenanceLog()
    {
        try
        {
            var response = await Http.GetAsync("api/DatabaseMaintenance/maintenance-log");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                maintenanceLog = JsonSerializer.Deserialize<List<MaintenanceLogEntry>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<MaintenanceLogEntry>();
            }
        }
        catch (Exception ex)
        {
            // Maintenance log loading is optional
        }
    }

    public class DatabaseHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string DatabaseSize { get; set; }
        public int TablesCount { get; set; }
        public DateTime LastCheckDate { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    public class DatabaseInfo
    {
        public string DatabaseName { get; set; }
        public string DatabaseType { get; set; }
        public string ServerVersion { get; set; }
        public string DataSize { get; set; }
        public string IndexSize { get; set; }
        public string UsedSpace { get; set; }
        public string FreeSpace { get; set; }
        public DateTime? LastBackupDate { get; set; }
    }

    public class MaintenanceLogEntry
    {
        public DateTime Date { get; set; }
        public string Action { get; set; }
        public string Status { get; set; }
        public string Duration { get; set; }
        public string Details { get; set; }
    }
}
