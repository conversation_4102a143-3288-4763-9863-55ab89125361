using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AppDev.Server.Models.AppDevDB
{
    [Table("App_CloudStorageProviders")]
    public class AppCloudStorageProvider
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ProviderId { get; set; }

        [Required]
        [StringLength(100)]
        public string ProviderName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ProviderType { get; set; } = string.Empty; // GoogleDrive, Mega, OneDrive, Dropbox

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(200)]
        public string? ApiEndpoint { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public bool RequiresAuthentication { get; set; } = true;

        [StringLength(100)]
        public string? SupportedFileTypes { get; set; } // JSON, SQL, ZIP

        public long? MaxFileSizeBytes { get; set; }

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime? LastModifiedDate { get; set; }

        [StringLength(450)]
        public string? CreatedByUserId { get; set; }

        [StringLength(450)]
        public string? LastModifiedByUserId { get; set; }

        // Navigation Properties
        public virtual ICollection<AppCloudStorageConfig> CloudStorageConfigs { get; set; } = new List<AppCloudStorageConfig>();
        public virtual ICollection<AppBackupCloudUpload> BackupCloudUploads { get; set; } = new List<AppBackupCloudUpload>();
    }
}
