using AppDev.Server.Data;
using AppDev.Server.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Reflection;

namespace AppDev.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DatabaseMaintenanceController : ControllerBase
    {
        private readonly AppDevDBContext _dbContext;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly string _backupFolderPath;

        public DatabaseMaintenanceController(AppDevDBContext dbContext, IWebHostEnvironment webHostEnvironment)
        {
            _dbContext = dbContext;
            _webHostEnvironment = webHostEnvironment;
            _backupFolderPath = Path.Combine(_webHostEnvironment.ContentRootPath, "Backups");

            if (!Directory.Exists(_backupFolderPath))
            {
                Directory.CreateDirectory(_backupFolderPath);
            }
        }

        [HttpPost("apply-migrations")]
        public async Task<IActionResult> ApplyMigrations()
        {
            try
            {
                await _dbContext.Database.MigrateAsync();
                return Ok(new MigrationResult { Success = true, Message = "تم تطبيق جميع التحديثات بنجاح" });
            }
            catch (Exception ex)
            {
                return Ok(new MigrationResult { Success = false, Message = $"حدث خطأ أثناء تطبيق التحديثات: {ex.Message}" });
            }
        }

        [HttpGet("health-check")]
        public async Task<IActionResult> GetDatabaseHealth()
        {
            try
            {
                var healthReport = new DatabaseHealthReport();

                // فحص الاتصال بقاعدة البيانات
                try
                {
                    await _dbContext.Database.OpenConnectionAsync();
                    healthReport.ConnectionStatus = true;
                    await _dbContext.Database.CloseConnectionAsync();
                }
                catch
                {
                    healthReport.ConnectionStatus = false;
                }

                // فحص الجداول المطلوبة
                healthReport.RequiredTables = await GetTableStatuses();

                // إحصائيات قاعدة البيانات
                healthReport.Statistics = GetDatabaseStatistics();

                // تحديد الحالة العامة
                healthReport.OverallHealth = CalculateOverallHealth(healthReport);

                return Ok(healthReport);
            }
            catch (Exception ex)
            {
                return Ok(new DatabaseHealthReport
                {
                    ConnectionStatus = false,
                    OverallHealth = "سيء",
                    RequiredTables = new List<TableStatus>(),
                    Statistics = new DatabaseStatistics()
                });
            }
        }

        [HttpPost("optimize-database")]
        public async Task<IActionResult> OptimizeDatabase()
        {
            try
            {
                var results = new List<string>();

                // تحسين الجداول
                var tableNames = _dbContext.Model.GetEntityTypes()
                    .Select(t => t.GetTableName())
                    .Where(t => t != null)
                    .Distinct()
                    .ToList();

                foreach (var tableName in tableNames)
                {
                    if (tableName != null)
                    {
                        try
                        {
                            await _dbContext.Database.ExecuteSqlRawAsync("OPTIMIZE TABLE `{0}`", tableName);
                            results.Add($"تم تحسين الجدول: {tableName}");
                        }
                        catch (Exception ex)
                        {
                            results.Add($"فشل في تحسين الجدول {tableName}: {ex.Message}");
                        }
                    }
                }

                return Ok(new { Success = true, Results = results });
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, Message = $"خطأ في تحسين قاعدة البيانات: {ex.Message}" });
            }
        }

        [HttpPost("repair-database")]
        public async Task<IActionResult> RepairDatabase()
        {
            try
            {
                var results = new List<string>();

                var tableNames = _dbContext.Model.GetEntityTypes()
                    .Select(t => t.GetTableName())
                    .Where(t => t != null)
                    .Distinct()
                    .ToList();

                foreach (var tableName in tableNames)
                {
                    if (tableName != null)
                    {
                        try
                        {
                            await _dbContext.Database.ExecuteSqlRawAsync("REPAIR TABLE `{0}`", tableName);
                            results.Add($"تم إصلاح الجدول: {tableName}");
                        }
                        catch (Exception ex)
                        {
                            results.Add($"فشل في إصلاح الجدول {tableName}: {ex.Message}");
                        }
                    }
                }

                return Ok(new { Success = true, Results = results });
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, Message = $"خطأ في إصلاح قاعدة البيانات: {ex.Message}" });
            }
        }

        [HttpGet("database-size")]
        public async Task<IActionResult> GetDatabaseSize()
        {
            try
            {
                var query = @"
                    SELECT 
                        table_name AS TableName,
                        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS SizeMB
                    FROM information_schema.TABLES 
                    WHERE table_schema = DATABASE()
                    ORDER BY (data_length + index_length) DESC";

                var connection = _dbContext.Database.GetDbConnection();
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = query;

                var tableSizes = new List<object>();
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    tableSizes.Add(new
                    {
                        TableName = reader.GetString("TableName"),
                        SizeMB = reader.GetDecimal("SizeMB")
                    });
                }

                await connection.CloseAsync();

                return Ok(tableSizes);
            }
            catch (Exception ex)
            {
                return Ok(new { Success = false, Message = $"خطأ في حساب حجم قاعدة البيانات: {ex.Message}" });
            }
        }

        // الوظائف المساعدة
        private async Task<List<TableStatus>> GetTableStatuses()
        {
            var tableStatusList = new List<TableStatus>();
            var tableNames = _dbContext.Model.GetEntityTypes()
                .Select(t => t.GetTableName())
                .Where(t => t != null)
                .Distinct()
                .ToList();

            foreach (var tableName in tableNames)
            {
                if (tableName != null)
                {
                    var status = new TableStatus { Name = tableName };
                    try
                    {
                        var entityType = _dbContext.Model.GetEntityTypes().FirstOrDefault(e => e.GetTableName() == tableName);
                        if (entityType != null)
                        {
                            status.RecordCount = await GetTableCount(entityType.ClrType);
                            status.Exists = true;
                        }
                        else
                        {
                            status.Exists = false;
                        }
                    }
                    catch
                    {
                        status.Exists = false;
                    }
                    tableStatusList.Add(status);
                }
            }
            return tableStatusList;
        }

        private DatabaseStatistics GetDatabaseStatistics()
        {
            var backupFiles = Directory.GetFiles(_backupFolderPath, "*.zip");
            var lastBackup = backupFiles.Any() ? new FileInfo(backupFiles.Max()!).CreationTime : (DateTime?)null;
            return new DatabaseStatistics
            {
                LastBackup = lastBackup,
                BackupCount = backupFiles.Length
            };
        }

        private string CalculateOverallHealth(DatabaseHealthReport report)
        {
            if (!report.ConnectionStatus) return "سيء";
            if (report.RequiredTables.Any(t => !t.Exists)) return "يحتاج صيانة";
            return "جيد";
        }

        private async Task<int> GetTableCount(Type entityType)
        {
            var method = typeof(DatabaseMaintenanceController).GetMethod("GetTableCountGeneric", BindingFlags.NonPublic | BindingFlags.Instance)?.MakeGenericMethod(entityType);
            if (method != null)
            {
                var task = (Task<int>)method.Invoke(this, new object[] { });
                return await task;
            }
            return 0;
        }

        private async Task<int> GetTableCountGeneric<T>() where T : class
        {
            return await _dbContext.Set<T>().CountAsync();
        }
    }
}
