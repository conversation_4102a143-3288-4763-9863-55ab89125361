using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace AppDev.Server.Services
{
    public class ErrorLogCleanupService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ErrorLogCleanupService> _logger;
        private readonly TimeSpan _period = TimeSpan.FromDays(1); // Check daily

        public ErrorLogCleanupService(
            IServiceProvider serviceProvider,
            ILogger<ErrorLogCleanupService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await DoWorkAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred executing error log cleanup");
                }

                await Task.Delay(_period, stoppingToken);
            }
        }

        private async Task DoWorkAsync()
        {
            var now = DateTime.UtcNow;
            
            // Run cleanup on the first day of each month at 2 AM
            if (now.Day == 1 && now.Hour == 2)
            {
                using var scope = _serviceProvider.CreateScope();
                var errorLoggingService = scope.ServiceProvider.GetRequiredService<IErrorLoggingService>();
                
                _logger.LogInformation("Starting monthly error log cleanup");
                await errorLoggingService.CleanupOldLogsAsync(30); // Keep logs for 30 days
                _logger.LogInformation("Monthly error log cleanup completed");
            }
        }
    }
}
