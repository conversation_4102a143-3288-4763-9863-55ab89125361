import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_app/app/routes/app_pages.dart';
import 'package:mobile_app/app/data/services/auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Get.putAsync(() => AuthService().init());
  runApp(
    GetMaterialApp(
      title: "Application",
      initialRoute: AppPages.initial, // Changed to lowerCamelCase
      getPages: AppPages.routes,
      debugShowCheckedModeBanner: false,
    ),
  );
}