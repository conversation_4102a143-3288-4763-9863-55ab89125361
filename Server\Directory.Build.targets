<Project>
  <!-- حل مشكلة تضارب ملفات .pdb في dotnet watch -->
  <Target Name="SkipCopyingPdbFiles" BeforeTargets="CopyFilesToOutputDirectory">
    <ItemGroup>
      <ReferenceCopyLocalPaths Remove="@(ReferenceCopyLocalPaths)" Condition="'%(Extension)' == '.pdb' AND '$(DOTNET_WATCH)' == '1'" />
    </ItemGroup>
  </Target>
  
  <!-- تجنب نسخ ملفات Client.pdb عند استخدام dotnet watch -->
  <Target Name="SkipClientPdbCopy" BeforeTargets="CopyFilesToOutputDirectory" Condition="'$(DOTNET_WATCH)' == '1'">
    <ItemGroup>
      <ReferenceCopyLocalPaths Remove="@(ReferenceCopyLocalPaths)" Condition="'%(Filename)' == 'AppDev.Client' AND '%(Extension)' == '.pdb'" />
    </ItemGroup>
  </Target>
</Project>
