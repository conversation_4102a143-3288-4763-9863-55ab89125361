@page "/"
@attribute [Authorize]
@inject NavigationManager Navigation

<PageTitle>لوحة التحكم - AppDev</PageTitle>

<RadzenStack Gap="2rem">
    <!-- Header Section -->
    <RadzenRow>
        <RadzenColumn Size="12">
            <RadzenText TextStyle="TextStyle.H3" TagName="TagName.H1" class="rz-pt-4">
                <RadzenIcon Icon="dashboard" class="rz-me-2" />
                مرحباً بك في نظام AppDev
            </RadzenText>
            <RadzenText TextStyle="TextStyle.Subtitle1" class="rz-color-secondary">
                نظام إدارة شامل للتقارير وقواعد البيانات
            </RadzenText>
        </RadzenColumn>
    </RadzenRow>

    <!-- Quick Access Cards -->
    <RadzenRow Gap="1.5rem">
        <!-- التقارير -->
        <RadzenColumn Size="12" SizeMD="6" SizeLG="3">
            <RadzenCard class="rz-p-4 rz-text-align-center"
                Style="cursor: pointer; min-height: 180px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;"
                @onclick="@(() => Navigation.NavigateTo("/reports"))">
                <RadzenStack AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="assessment" Style="font-size: 3rem;" />
                    <RadzenText TextStyle="TextStyle.H5" class="rz-mb-0">التقارير</RadzenText>
                    <RadzenText TextStyle="TextStyle.Caption">إدارة وتصميم التقارير</RadzenText>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <!-- إدارة المستخدمين -->
        <RadzenColumn Size="12" SizeMD="6" SizeLG="3">
            <RadzenCard class="rz-p-4 rz-text-align-center"
                Style="cursor: pointer; min-height: 180px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;"
                @onclick="@(() => Navigation.NavigateTo("/administration/users"))">
                <RadzenStack AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="group" Style="font-size: 3rem;" />
                    <RadzenText TextStyle="TextStyle.H5" class="rz-mb-0">المستخدمون</RadzenText>
                    <RadzenText TextStyle="TextStyle.Caption">إدارة المستخدمين والأدوار</RadzenText>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <!-- النسخ الاحتياطي -->
        <RadzenColumn Size="12" SizeMD="6" SizeLG="3">
            <RadzenCard class="rz-p-4 rz-text-align-center"
                Style="cursor: pointer; min-height: 180px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;"
                @onclick="@(() => Navigation.NavigateTo("/admin/database/backup-management"))">
                <RadzenStack AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="backup" Style="font-size: 3rem;" />
                    <RadzenText TextStyle="TextStyle.H5" class="rz-mb-0">النسخ الاحتياطي</RadzenText>
                    <RadzenText TextStyle="TextStyle.Caption">إدارة النسخ الاحتياطية</RadzenText>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <!-- إدارة النظام -->
        <RadzenColumn Size="12" SizeMD="6" SizeLG="3">
            <RadzenCard class="rz-p-4 rz-text-align-center"
                Style="cursor: pointer; min-height: 180px; background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;"
                @onclick="@(() => Navigation.NavigateTo("/database-maintenance"))">
                <RadzenStack AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="settings" Style="font-size: 3rem;" />
                    <RadzenText TextStyle="TextStyle.H5" class="rz-mb-0">إدارة النظام</RadzenText>
                    <RadzenText TextStyle="TextStyle.Caption">صيانة قاعدة البيانات</RadzenText>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
    </RadzenRow>

    <!-- System Status -->
    <RadzenRow Gap="1.5rem">
        <RadzenColumn Size="12" SizeMD="6">
            <RadzenCard class="rz-p-4">
                <RadzenText TextStyle="TextStyle.H5" class="rz-mb-3">
                    <RadzenIcon Icon="health_and_safety" class="rz-me-2" />
                    حالة النظام
                </RadzenText>

                <RadzenStack Gap="1rem">
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center">
                        <RadzenText TextStyle="TextStyle.Body1">قاعدة البيانات</RadzenText>
                        <RadzenBadge BadgeStyle="BadgeStyle.Success" Text="متصلة" />
                    </RadzenStack>

                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center">
                        <RadzenText TextStyle="TextStyle.Body1">النسخ الاحتياطي التلقائي</RadzenText>
                        <RadzenBadge BadgeStyle="BadgeStyle.Success" Text="نشط" />
                    </RadzenStack>

                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center">
                        <RadzenText TextStyle="TextStyle.Body1">التخزين السحابي</RadzenText>
                        <RadzenBadge BadgeStyle="BadgeStyle.Warning" Text="غير مكون" />
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="6">
            <RadzenCard class="rz-p-4">
                <RadzenText TextStyle="TextStyle.H5" class="rz-mb-3">
                    <RadzenIcon Icon="timeline" class="rz-me-2" />
                    الإجراءات السريعة
                </RadzenText>

                <RadzenStack Gap="0.75rem">
                    <RadzenButton Text="إنشاء تقرير جديد" Icon="add_circle" ButtonStyle="ButtonStyle.Primary" Size="ButtonSize.Small"
                        Click="@(() => Navigation.NavigateTo("/report-designer"))" Style="width: 100%;" />

                    <RadzenButton Text="نسخة احتياطية فورية" Icon="backup" ButtonStyle="ButtonStyle.Success" Size="ButtonSize.Small"
                        Click="@(() => Navigation.NavigateTo("/admin/database/backup-management"))" Style="width: 100%;" />

                    <RadzenButton Text="إضافة مستخدم جديد" Icon="person_add" ButtonStyle="ButtonStyle.Info" Size="ButtonSize.Small"
                        Click="@(() => Navigation.NavigateTo("/administration/users"))" Style="width: 100%;" />

                    <RadzenButton Text="إعدادات التخزين السحابي" Icon="cloud" ButtonStyle="ButtonStyle.Secondary" Size="ButtonSize.Small"
                        Click="@(() => Navigation.NavigateTo("/admin/cloudstorage/cloud-storage-config"))" Style="width: 100%;" />
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
    </RadzenRow>

    <!-- Recent Activity -->
    <RadzenRow>
        <RadzenColumn Size="12">
            <RadzenCard class="rz-p-4">
                <RadzenText TextStyle="TextStyle.H5" class="rz-mb-3">
                    <RadzenIcon Icon="history" class="rz-me-2" />
                    النشاط الأخير
                </RadzenText>

                <RadzenStack Gap="1rem">
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" AlignItems="AlignItems.Center">
                        <RadzenIcon Icon="backup" Style="color: var(--rz-success); font-size: 1.5rem;" />
                        <RadzenStack Gap="0.25rem">
                            <RadzenText TextStyle="TextStyle.Body2" class="rz-mb-0">تم إنشاء نسخة احتياطية تلقائية</RadzenText>
                            <RadzenText TextStyle="TextStyle.Caption" class="rz-color-secondary">منذ دقائق قليلة</RadzenText>
                        </RadzenStack>
                    </RadzenStack>

                    <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" AlignItems="AlignItems.Center">
                        <RadzenIcon Icon="login" Style="color: var(--rz-info); font-size: 1.5rem;" />
                        <RadzenStack Gap="0.25rem">
                            <RadzenText TextStyle="TextStyle.Body2" class="rz-mb-0">تسجيل دخول جديد للمدير</RadzenText>
                            <RadzenText TextStyle="TextStyle.Caption" class="rz-color-secondary">الآن</RadzenText>
                        </RadzenStack>
                    </RadzenStack>

                    <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" AlignItems="AlignItems.Center">
                        <RadzenIcon Icon="system_update" Style="color: var(--rz-primary); font-size: 1.5rem;" />
                        <RadzenStack Gap="0.25rem">
                            <RadzenText TextStyle="TextStyle.Body2" class="rz-mb-0">تحديث النظام</RadzenText>
                            <RadzenText TextStyle="TextStyle.Caption" class="rz-color-secondary">اليوم</RadzenText>
                        </RadzenStack>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
    </RadzenRow>
</RadzenStack>
