@page "/admin/logs/error-logs"
@using AppDev.Server.Models.AppDevDB
@using System.Text.Json
@inject HttpClient Http
@inject NotificationService NotificationService
@inject DialogService DialogService
@inject IJSRuntime JSRuntime

<PageTitle>سجل الأخطاء</PageTitle>

<RadzenStack Gap="1rem">
    <RadzenRow>
        <RadzenColumn Size="12">
            <RadzenText TextStyle="TextStyle.H3" TagName="TagName.H1" class="rz-pt-8">
                <RadzenIcon Icon="error" class="rz-me-1" />
                سجل الأخطاء
            </RadzenText>
        </RadzenColumn>
    </RadzenRow>

    <!-- Statistics Cards -->
    <RadzenRow Gap="1rem">
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="error" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">@statistics?.TotalErrors</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">إجمالي الأخطاء</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
        
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4" Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="warning" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">@statistics?.UnresolvedErrors</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">أخطاء غير محلولة</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
        
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4" Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="today" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">@statistics?.ErrorsToday</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">أخطاء اليوم</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
        
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4" Style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="date_range" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">@statistics?.ErrorsThisWeek</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">أخطاء هذا الأسبوع</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
    </RadzenRow>

    <!-- Filters -->
    <RadzenCard class="rz-p-4">
        <RadzenRow Gap="1rem" AlignItems="AlignItems.End">
            <RadzenColumn Size="12" SizeMD="2">
                <RadzenLabel Text="مستوى الخطأ" />
                <RadzenDropDown @bind-Value="selectedLogLevel" Data="logLevels" 
                    TextProperty="Text" ValueProperty="Value" 
                    Placeholder="جميع المستويات" AllowClear="true"
                    Change="@(() => LoadErrorLogs())" />
            </RadzenColumn>
            
            <RadzenColumn Size="12" SizeMD="2">
                <RadzenLabel Text="الحالة" />
                <RadzenDropDown @bind-Value="selectedResolved" Data="resolvedOptions" 
                    TextProperty="Text" ValueProperty="Value" 
                    Placeholder="جميع الحالات" AllowClear="true"
                    Change="@(() => LoadErrorLogs())" />
            </RadzenColumn>
            
            <RadzenColumn Size="12" SizeMD="2">
                <RadzenLabel Text="من تاريخ" />
                <RadzenDatePicker @bind-Value="fromDate" DateFormat="yyyy-MM-dd" 
                    Change="@(() => LoadErrorLogs())" />
            </RadzenColumn>
            
            <RadzenColumn Size="12" SizeMD="2">
                <RadzenLabel Text="إلى تاريخ" />
                <RadzenDatePicker @bind-Value="toDate" DateFormat="yyyy-MM-dd" 
                    Change="@(() => LoadErrorLogs())" />
            </RadzenColumn>
            
            <RadzenColumn Size="12" SizeMD="2">
                <RadzenButton Text="تحديث" Icon="refresh" Click="@(() => LoadErrorLogs())" 
                    ButtonStyle="ButtonStyle.Primary" />
            </RadzenColumn>
            
            <RadzenColumn Size="12" SizeMD="2">
                <RadzenButton Text="تصدير" Icon="download" Click="@ExportLogs" 
                    ButtonStyle="ButtonStyle.Secondary" />
            </RadzenColumn>
        </RadzenRow>
    </RadzenCard>

    <!-- Error Logs Table -->
    <RadzenCard class="rz-p-4">
        <RadzenDataGrid @ref="errorLogsGrid" Data="@errorLogs" TItem="AppErrorLog" 
            AllowPaging="true" PageSize="20" AllowSorting="true" AllowFiltering="true"
            LoadData="@LoadErrorLogsData" Count="@totalCount" IsLoading="@isLoading">
            
            <Columns>
                <RadzenDataGridColumn TItem="AppErrorLog" Property="CreatedDate" Title="التاريخ" Width="150px">
                    <Template Context="log">
                        @log.CreatedDate.ToString("yyyy-MM-dd HH:mm")
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="AppErrorLog" Property="LogLevel" Title="المستوى" Width="100px">
                    <Template Context="log">
                        <RadzenBadge BadgeStyle="@GetLogLevelBadgeStyle(log.LogLevel)" Text="@log.LogLevel" />
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="AppErrorLog" Property="Message" Title="الرسالة" Width="300px">
                    <Template Context="log">
                        <RadzenText TextStyle="TextStyle.Body2" class="rz-text-truncate" 
                            Title="@log.Message">@log.Message</RadzenText>
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="AppErrorLog" Property="Source" Title="المصدر" Width="150px" />
                
                <RadzenDataGridColumn TItem="AppErrorLog" Property="IsResolved" Title="الحالة" Width="100px">
                    <Template Context="log">
                        @if (log.IsResolved)
                        {
                            <RadzenBadge BadgeStyle="BadgeStyle.Success" Text="محلول" />
                        }
                        else
                        {
                            <RadzenBadge BadgeStyle="BadgeStyle.Danger" Text="غير محلول" />
                        }
                    </Template>
                </RadzenDataGridColumn>
                
                <RadzenDataGridColumn TItem="AppErrorLog" Title="الإجراءات" Width="150px" Sortable="false" Filterable="false">
                    <Template Context="log">
                        <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem">
                            <RadzenButton Icon="visibility" Size="ButtonSize.Small" 
                                ButtonStyle="ButtonStyle.Info" 
                                Click="@(() => ViewErrorDetails(log))" 
                                Title="عرض التفاصيل" />
                            
                            @if (!log.IsResolved)
                            {
                                <RadzenButton Icon="check" Size="ButtonSize.Small" 
                                    ButtonStyle="ButtonStyle.Success" 
                                    Click="@(() => ResolveError(log))" 
                                    Title="تحديد كمحلول" />
                            }
                        </RadzenStack>
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenCard>
</RadzenStack>

@code {
    private RadzenDataGrid<AppErrorLog> errorLogsGrid;
    private List<AppErrorLog> errorLogs = new();
    private ErrorStatistics statistics;
    private int totalCount;
    private bool isLoading = false;

    // Filter properties
    private string selectedLogLevel;
    private bool? selectedResolved;
    private DateTime? fromDate;
    private DateTime? toDate;

    // Dropdown data
    private List<DropdownItem> logLevels = new()
    {
        new DropdownItem { Text = "خطأ", Value = "Error" },
        new DropdownItem { Text = "تحذير", Value = "Warning" },
        new DropdownItem { Text = "معلومات", Value = "Information" },
        new DropdownItem { Text = "تصحيح", Value = "Debug" }
    };

    private List<DropdownItem> resolvedOptions = new()
    {
        new DropdownItem { Text = "محلول", Value = true },
        new DropdownItem { Text = "غير محلول", Value = false }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadStatistics();
        await LoadErrorLogs();
    }

    private async Task LoadStatistics()
    {
        try
        {
            var response = await Http.GetAsync("api/ErrorLog/statistics");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                statistics = JsonSerializer.Deserialize<ErrorStatistics>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = "فشل في تحميل الإحصائيات"
            });
        }
    }

    private async Task LoadErrorLogs()
    {
        if (errorLogsGrid != null)
        {
            await errorLogsGrid.FirstPage();
        }
    }

    private async Task LoadErrorLogsData(LoadDataArgs args)
    {
        isLoading = true;
        try
        {
            var page = (args.Skip ?? 0) / (args.Top ?? 20) + 1;
            var pageSize = args.Top ?? 20;

            var queryParams = new List<string>
            {
                $"page={page}",
                $"pageSize={pageSize}"
            };

            if (!string.IsNullOrEmpty(selectedLogLevel))
                queryParams.Add($"logLevel={selectedLogLevel}");

            if (selectedResolved.HasValue)
                queryParams.Add($"isResolved={selectedResolved.Value}");

            if (fromDate.HasValue)
                queryParams.Add($"fromDate={fromDate.Value:yyyy-MM-dd}");

            if (toDate.HasValue)
                queryParams.Add($"toDate={toDate.Value:yyyy-MM-dd}");

            var query = string.Join("&", queryParams);
            var response = await Http.GetAsync($"api/ErrorLog/user-logs?{query}");

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                errorLogs = JsonSerializer.Deserialize<List<AppErrorLog>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                }) ?? new List<AppErrorLog>();

                if (response.Headers.TryGetValues("X-Total-Count", out var totalCountValues))
                {
                    int.TryParse(totalCountValues.First(), out totalCount);
                }
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = "فشل في تحميل سجل الأخطاء"
            });
        }
        finally
        {
            isLoading = false;
        }
    }

    private BadgeStyle GetLogLevelBadgeStyle(string logLevel)
    {
        return logLevel switch
        {
            "Error" => BadgeStyle.Danger,
            "Warning" => BadgeStyle.Warning,
            "Information" => BadgeStyle.Info,
            "Debug" => BadgeStyle.Secondary,
            _ => BadgeStyle.Light
        };
    }

    private async Task ViewErrorDetails(AppErrorLog log)
    {
        await DialogService.OpenAsync<ErrorDetailsDialog>("تفاصيل الخطأ",
            new Dictionary<string, object> { { "ErrorLog", log } },
            new DialogOptions() { Width = "800px", Height = "600px" });
    }

    private async Task ResolveError(AppErrorLog log)
    {
        var result = await DialogService.OpenAsync<ResolveErrorDialog>("تحديد الخطأ كمحلول",
            new Dictionary<string, object> { { "ErrorLog", log } },
            new DialogOptions() { Width = "500px", Height = "300px" });

        if (result == true)
        {
            await LoadErrorLogs();
            await LoadStatistics();
        }
    }

    private async Task ExportLogs()
    {
        try
        {
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(selectedLogLevel))
                queryParams.Add($"logLevel={selectedLogLevel}");

            if (selectedResolved.HasValue)
                queryParams.Add($"isResolved={selectedResolved.Value}");

            if (fromDate.HasValue)
                queryParams.Add($"fromDate={fromDate.Value:yyyy-MM-dd}");

            if (toDate.HasValue)
                queryParams.Add($"toDate={toDate.Value:yyyy-MM-dd}");

            var query = string.Join("&", queryParams);
            var url = $"api/ErrorLog/user-logs?pageSize=10000&{query}";

            await JSRuntime.InvokeVoidAsync("open", url, "_blank");

            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Success,
                Summary = "نجح",
                Detail = "تم تصدير سجل الأخطاء بنجاح"
            });
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = "فشل في تصدير سجل الأخطاء"
            });
        }
    }

    public class DropdownItem
    {
        public string Text { get; set; }
        public object Value { get; set; }
    }

    public class ErrorStatistics
    {
        public int TotalErrors { get; set; }
        public int UnresolvedErrors { get; set; }
        public int ErrorsToday { get; set; }
        public int ErrorsThisWeek { get; set; }
        public int ErrorsThisMonth { get; set; }
        public List<ErrorLevelCount> ErrorsByLevel { get; set; } = new();
    }

    public class ErrorLevelCount
    {
        public string Level { get; set; }
        public int Count { get; set; }
    }
}
