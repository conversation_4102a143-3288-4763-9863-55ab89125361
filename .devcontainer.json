{"image": "mcr.microsoft.com/devcontainers/base:ubuntu", "features": {"ghcr.io/devcontainers/features/dotnet:2": {"version": "8.0.100"}, "ghcr.io/devcontainers/features/docker-in-docker:2": {"version": "latest", "enableNonRootDocker": "true"}}, "customizations": {"vscode": {"extensions": ["./.vscode/augment.vscode-augment-0.521.1.vsix", "google.geminicodeassist", "ms-azuretools.vscode-docker", "ms-dotnettools.csharp", "ms-dotnettools.vscode-dotnet-runtime", "pkief.material-icon-theme", "pkief.material-product-icons", "ms-vscode.vscode-json"], "settings": {"workbench.productIconTheme": "material-product-icons", "workbench.iconTheme": "material-icon-theme", "workbench.colorTheme": "Default Dark+", "geminicodeassist.updateChannel": "Insiders", "geminicodeassist.inlineSuggestions.enableAuto": true, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "editor.formatOnSave": true, "editor.tabSize": 4, "editor.insertSpaces": true, "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000}}}, "postCreateCommand": "sudo chmod -R 777 . && sudo chown -R vscode:vscode . && sudo chown -R vscode:vscode /workspaces/app && sudo usermod -aG docker $USER && sudo chmod 666 /var/run/docker.sock && dotnet --version && docker --version", "postStartCommand": "sudo chmod -R 777 . && sudo chown -R vscode:vscode . && sudo chown -R vscode:vscode /workspaces/app && sudo usermod -aG docker $USER && sudo chmod 666 /var/run/docker.sock && dotnet --version && docker --version", "mounts": ["source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"], "runArgs": ["--privileged"]}