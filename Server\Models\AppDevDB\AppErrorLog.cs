using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AppDev.Server.Models.AppDevDB
{
    [Table("App_ErrorLogs")]
    public partial class AppErrorLog
    {
        [NotMapped]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("@odata.etag")]
        public string ETag { get; set; }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long ErrorId { get; set; }

        [Required]
        [ConcurrencyCheck]
        [MaxLength(450)]
        public string UserId { get; set; }

        [Required]
        [ConcurrencyCheck]
        [MaxLength(100)]
        public string LogLevel { get; set; } // Error, Warning, Information, Debug

        [Required]
        [ConcurrencyCheck]
        [MaxLength(500)]
        public string Message { get; set; }

        [ConcurrencyCheck]
        public string Exception { get; set; } // Full exception details

        [ConcurrencyCheck]
        [MaxLength(200)]
        public string Source { get; set; } // Controller/Page name

        [ConcurrencyCheck]
        [MaxLength(500)]
        public string RequestPath { get; set; }

        [ConcurrencyCheck]
        [MaxLength(50)]
        public string HttpMethod { get; set; }

        [ConcurrencyCheck]
        [MaxLength(45)]
        public string IpAddress { get; set; }

        [ConcurrencyCheck]
        [MaxLength(500)]
        public string UserAgent { get; set; }

        [ConcurrencyCheck]
        public string AdditionalData { get; set; } // JSON format for extra data

        [Required]
        [ConcurrencyCheck]
        public DateTime CreatedDate { get; set; }

        [ConcurrencyCheck]
        [MaxLength(100)]
        public string MachineName { get; set; }

        [ConcurrencyCheck]
        [MaxLength(100)]
        public string ApplicationName { get; set; } = "AppDev";

        [ConcurrencyCheck]
        public bool IsResolved { get; set; } = false;

        [ConcurrencyCheck]
        public DateTime? ResolvedDate { get; set; }

        [ConcurrencyCheck]
        [MaxLength(450)]
        public string ResolvedByUserId { get; set; }

        [ConcurrencyCheck]
        public string ResolutionNotes { get; set; }
    }
}
