@using AppDev.Server.Models.AppDevDB
@inject DialogService DialogService

<RadzenStack Gap="1rem">
    <RadzenCard class="rz-p-4">
        <RadzenStack Gap="1rem">
            <RadzenRow>
                <RadzenColumn Size="6">
                    <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">معرف الخطأ:</RadzenText>
                    <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.ErrorId</RadzenText>
                </RadzenColumn>
                <RadzenColumn Size="6">
                    <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">التاريخ:</RadzenText>
                    <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss")</RadzenText>
                </RadzenColumn>
            </RadzenRow>

            <RadzenRow>
                <RadzenColumn Size="6">
                    <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">مستوى الخطأ:</RadzenText>
                    <RadzenBadge BadgeStyle="@GetLogLevelBadgeStyle(ErrorLog?.LogLevel)" Text="@ErrorLog?.LogLevel" />
                </RadzenColumn>
                <RadzenColumn Size="6">
                    <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">المصدر:</RadzenText>
                    <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.Source</RadzenText>
                </RadzenColumn>
            </RadzenRow>

            <RadzenRow>
                <RadzenColumn Size="12">
                    <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">الرسالة:</RadzenText>
                    <RadzenCard class="rz-p-3" Style="background-color: var(--rz-base-100);">
                        <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.Message</RadzenText>
                    </RadzenCard>
                </RadzenColumn>
            </RadzenRow>

            @if (!string.IsNullOrEmpty(ErrorLog?.RequestPath))
            {
                <RadzenRow>
                    <RadzenColumn Size="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">مسار الطلب:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.RequestPath</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">طريقة HTTP:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.HttpMethod</RadzenText>
                    </RadzenColumn>
                </RadzenRow>
            }

            @if (!string.IsNullOrEmpty(ErrorLog?.IpAddress))
            {
                <RadzenRow>
                    <RadzenColumn Size="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">عنوان IP:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.IpAddress</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">اسم الجهاز:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.MachineName</RadzenText>
                    </RadzenColumn>
                </RadzenRow>
            }

            @if (!string.IsNullOrEmpty(ErrorLog?.Exception))
            {
                <RadzenRow>
                    <RadzenColumn Size="12">
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">تفاصيل الاستثناء:</RadzenText>
                        <RadzenCard class="rz-p-3" Style="background-color: var(--rz-danger-lighter); max-height: 300px; overflow-y: auto;">
                            <pre style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 12px;">@ErrorLog?.Exception</pre>
                        </RadzenCard>
                    </RadzenColumn>
                </RadzenRow>
            }

            @if (!string.IsNullOrEmpty(ErrorLog?.UserAgent))
            {
                <RadzenRow>
                    <RadzenColumn Size="12">
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">معلومات المتصفح:</RadzenText>
                        <RadzenCard class="rz-p-3" Style="background-color: var(--rz-base-100);">
                            <RadzenText TextStyle="TextStyle.Body2">@ErrorLog?.UserAgent</RadzenText>
                        </RadzenCard>
                    </RadzenColumn>
                </RadzenRow>
            }

            @if (!string.IsNullOrEmpty(ErrorLog?.AdditionalData))
            {
                <RadzenRow>
                    <RadzenColumn Size="12">
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">بيانات إضافية:</RadzenText>
                        <RadzenCard class="rz-p-3" Style="background-color: var(--rz-base-100); max-height: 200px; overflow-y: auto;">
                            <pre style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 12px;">@ErrorLog?.AdditionalData</pre>
                        </RadzenCard>
                    </RadzenColumn>
                </RadzenRow>
            }

            @if (ErrorLog?.IsResolved == true)
            {
                <RadzenRow>
                    <RadzenColumn Size="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">تاريخ الحل:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.ResolvedDate?.ToString("yyyy-MM-dd HH:mm:ss")</RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                        <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">تم الحل بواسطة:</RadzenText>
                        <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.ResolvedByUserId</RadzenText>
                    </RadzenColumn>
                </RadzenRow>

                @if (!string.IsNullOrEmpty(ErrorLog?.ResolutionNotes))
                {
                    <RadzenRow>
                        <RadzenColumn Size="12">
                            <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">ملاحظات الحل:</RadzenText>
                            <RadzenCard class="rz-p-3" Style="background-color: var(--rz-success-lighter);">
                                <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.ResolutionNotes</RadzenText>
                            </RadzenCard>
                        </RadzenColumn>
                    </RadzenRow>
                }
            }
        </RadzenStack>
    </RadzenCard>

    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" Gap="1rem">
        @if (ErrorLog?.IsResolved != true)
        {
            <RadzenButton Text="تحديد كمحلول" Icon="check" ButtonStyle="ButtonStyle.Success" 
                Click="@(() => ResolveError())" />
        }
        <RadzenButton Text="إغلاق" Icon="close" ButtonStyle="ButtonStyle.Secondary" 
            Click="@(() => DialogService.Close())" />
    </RadzenStack>
</RadzenStack>

@code {
    [Parameter] public AppErrorLog ErrorLog { get; set; }

    private BadgeStyle GetLogLevelBadgeStyle(string logLevel)
    {
        return logLevel switch
        {
            "Error" => BadgeStyle.Danger,
            "Warning" => BadgeStyle.Warning,
            "Information" => BadgeStyle.Info,
            "Debug" => BadgeStyle.Secondary,
            _ => BadgeStyle.Light
        };
    }

    private async Task ResolveError()
    {
        DialogService.Close(true);
    }
}
