using Google.Apis.Auth.OAuth2;
using Google.Apis.Drive.v3;
using Google.Apis.Drive.v3.Data;
using Google.Apis.Services;
using Google.Apis.Upload;
using AppDev.Server.Data;
using AppDev.Server.Models;
using AppDev.Server.Models.AppDevDB;
using Microsoft.EntityFrameworkCore;

namespace AppDev.Server.Services
{
    public class GoogleDriveService : ICloudStorageService
    {
        private readonly AppDevDBContext _dbContext;
        private readonly ILogger<GoogleDriveService> _logger;

        public GoogleDriveService(AppDevDBContext dbContext, ILogger<GoogleDriveService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<CloudStorageTestResult> TestConnectionAsync(int configId)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null)
                {
                    return new CloudStorageTestResult
                    {
                        Success = false,
                        Message = "إعدادات التخزين السحابي غير موجودة",
                        ErrorCode = "CONFIG_NOT_FOUND"
                    };
                }

                var driveService = await CreateDriveServiceAsync(config);
                if (driveService == null)
                {
                    return new CloudStorageTestResult
                    {
                        Success = false,
                        Message = "فشل في إنشاء خدمة Google Drive",
                        ErrorCode = "SERVICE_CREATION_FAILED"
                    };
                }

                // Test by getting user info
                var aboutRequest = driveService.About.Get();
                aboutRequest.Fields = "user,storageQuota";
                var about = await aboutRequest.ExecuteAsync();

                return new CloudStorageTestResult
                {
                    Success = true,
                    Message = $"تم الاتصال بنجاح مع Google Drive للمستخدم: {about.User.DisplayName}",
                    Details = new Dictionary<string, object>
                    {
                        ["UserName"] = about.User.DisplayName ?? "غير محدد",
                        ["UserEmail"] = about.User.EmailAddress ?? "غير محدد",
                        ["StorageLimit"] = about.StorageQuota?.Limit?.ToString() ?? "غير محدود",
                        ["StorageUsage"] = about.StorageQuota?.Usage?.ToString() ?? "0"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اختبار الاتصال مع Google Drive للإعدادات {ConfigId}", configId);
                return new CloudStorageTestResult
                {
                    Success = false,
                    Message = $"خطأ في الاتصال: {ex.Message}",
                    ErrorCode = "CONNECTION_ERROR"
                };
            }
        }

        public async Task<CloudUploadResult> UploadFileAsync(CloudUploadRequest request)
        {
            try
            {
                var config = await GetConfigAsync(request.ConfigId);
                if (config == null)
                {
                    return new CloudUploadResult
                    {
                        Success = false,
                        Message = "إعدادات التخزين السحابي غير موجودة",
                        ErrorCode = "CONFIG_NOT_FOUND"
                    };
                }

                var driveService = await CreateDriveServiceAsync(config);
                if (driveService == null)
                {
                    return new CloudUploadResult
                    {
                        Success = false,
                        Message = "فشل في إنشاء خدمة Google Drive",
                        ErrorCode = "SERVICE_CREATION_FAILED"
                    };
                }

                // Create upload record
                var uploadRecord = new AppBackupCloudUpload
                {
                    ConfigId = request.ConfigId,
                    ProviderId = config.ProviderId,
                    LocalFilePath = request.LocalFilePath,
                    LocalFileName = Path.GetFileName(request.LocalFilePath),
                    CloudFileName = request.CustomFileName ?? Path.GetFileName(request.LocalFilePath),
                    BackupType = request.BackupType,
                    FileSizeBytes = new FileInfo(request.LocalFilePath).Length,
                    UploadStatus = "InProgress",
                    UploadStartTime = DateTime.UtcNow,
                    Tags = request.Tags,
                    Notes = request.Notes,
                    CreatedDate = DateTime.UtcNow
                };

                _dbContext.AppBackupCloudUploads.Add(uploadRecord);
                await _dbContext.SaveChangesAsync();

                // Ensure folder exists
                var folderId = await EnsureFolderExistsAsync(driveService, config.FolderPath ?? "/AppDev/Backups");

                // Upload file
                var fileMetadata = new Google.Apis.Drive.v3.Data.File()
                {
                    Name = uploadRecord.CloudFileName,
                    Parents = new List<string> { folderId }
                };

                using var stream = new FileStream(request.LocalFilePath, FileMode.Open, FileAccess.Read);
                var uploadRequest = driveService.Files.Create(fileMetadata, stream, GetMimeType(request.LocalFilePath));
                uploadRequest.Fields = "id,name,size,webViewLink";

                var uploadResult = await uploadRequest.UploadAsync();
                if (uploadResult.Status == Google.Apis.Upload.UploadStatus.Completed)
                {
                    var uploadedFile = uploadRequest.ResponseBody;

                    // Update upload record
                    uploadRecord.CloudFileId = uploadedFile.Id;
                    uploadRecord.CloudFileUrl = uploadedFile.WebViewLink;
                    uploadRecord.UploadStatus = "Completed";
                    uploadRecord.UploadEndTime = DateTime.UtcNow;
                    uploadRecord.UploadDuration = uploadRecord.UploadEndTime - uploadRecord.UploadStartTime;
                    uploadRecord.UploadProgress = 100;

                    await _dbContext.SaveChangesAsync();

                    // Delete local file if requested
                    if (request.DeleteLocalAfterUpload && System.IO.File.Exists(request.LocalFilePath))
                    {
                        System.IO.File.Delete(request.LocalFilePath);
                    }

                    return new CloudUploadResult
                    {
                        Success = true,
                        Message = "تم رفع الملف بنجاح إلى Google Drive",
                        UploadId = uploadRecord.UploadId,
                        CloudFileId = uploadedFile.Id,
                        CloudFileUrl = uploadedFile.WebViewLink,
                        FileSizeBytes = uploadedFile.Size
                    };
                }
                else
                {
                    uploadRecord.UploadStatus = "Failed";
                    uploadRecord.ErrorMessage = $"فشل الرفع: {uploadResult.Exception?.Message}";
                    uploadRecord.UploadEndTime = DateTime.UtcNow;
                    await _dbContext.SaveChangesAsync();

                    return new CloudUploadResult
                    {
                        Success = false,
                        Message = $"فشل في رفع الملف: {uploadResult.Exception?.Message}",
                        ErrorCode = "UPLOAD_FAILED"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في رفع الملف إلى Google Drive");
                return new CloudUploadResult
                {
                    Success = false,
                    Message = $"خطأ في رفع الملف: {ex.Message}",
                    ErrorCode = "UPLOAD_ERROR"
                };
            }
        }

        public async Task<bool> DeleteFileAsync(int configId, string cloudFileId)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return false;

                var driveService = await CreateDriveServiceAsync(config);
                if (driveService == null) return false;

                await driveService.Files.Delete(cloudFileId).ExecuteAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الملف من Google Drive");
                return false;
            }
        }

        public async Task<CloudStorageQuotaInfo> GetQuotaInfoAsync(int configId)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return new CloudStorageQuotaInfo();

                var driveService = await CreateDriveServiceAsync(config);
                if (driveService == null) return new CloudStorageQuotaInfo();

                var aboutRequest = driveService.About.Get();
                aboutRequest.Fields = "storageQuota";
                var about = await aboutRequest.ExecuteAsync();

                var quota = about.StorageQuota;
                var total = quota?.Limit ?? 0;
                var used = quota?.Usage ?? 0;
                var available = total - used;

                return new CloudStorageQuotaInfo
                {
                    TotalBytes = total,
                    UsedBytes = used,
                    AvailableBytes = available,
                    UsagePercentage = total > 0 ? (double)used / total * 100 : 0,
                    FormattedTotal = FormatFileSize(total),
                    FormattedUsed = FormatFileSize(used),
                    FormattedAvailable = FormatFileSize(available)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معلومات المساحة من Google Drive");
                return new CloudStorageQuotaInfo();
            }
        }

        public async Task<List<CloudFileInfo>> ListFilesAsync(int configId, string? folderPath = null)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return new List<CloudFileInfo>();

                var driveService = await CreateDriveServiceAsync(config);
                if (driveService == null) return new List<CloudFileInfo>();

                var folderId = await GetFolderIdAsync(driveService, folderPath ?? config.FolderPath ?? "/AppDev/Backups");

                var listRequest = driveService.Files.List();
                listRequest.Q = $"'{folderId}' in parents and trashed=false";
                listRequest.Fields = "files(id,name,size,createdTime,modifiedTime,mimeType,webViewLink)";

                var files = await listRequest.ExecuteAsync();

                return files.Files.Select(f => new CloudFileInfo
                {
                    Id = f.Id,
                    Name = f.Name,
                    Size = f.Size ?? 0,
                    CreatedDate = f.CreatedTimeDateTimeOffset?.DateTime ?? DateTime.MinValue,
                    ModifiedDate = f.ModifiedTimeDateTimeOffset?.DateTime ?? DateTime.MinValue,
                    IsFolder = f.MimeType == "application/vnd.google-apps.folder",
                    MimeType = f.MimeType,
                    ShareUrl = f.WebViewLink
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب قائمة الملفات من Google Drive");
                return new List<CloudFileInfo>();
            }
        }

        public async Task<Stream> DownloadFileAsync(int configId, string cloudFileId)
        {
            var config = await GetConfigAsync(configId);
            if (config == null) throw new InvalidOperationException("إعدادات التخزين السحابي غير موجودة");

            var driveService = await CreateDriveServiceAsync(config);
            if (driveService == null) throw new InvalidOperationException("فشل في إنشاء خدمة Google Drive");

            var request = driveService.Files.Get(cloudFileId);
            return await request.ExecuteAsStreamAsync();
        }

        public async Task<bool> CreateFolderAsync(int configId, string folderPath)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return false;

                var driveService = await CreateDriveServiceAsync(config);
                if (driveService == null) return false;

                await EnsureFolderExistsAsync(driveService, folderPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المجلد في Google Drive");
                return false;
            }
        }

        public async Task<string> GetShareLinkAsync(int configId, string cloudFileId)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return string.Empty;

                var driveService = await CreateDriveServiceAsync(config);
                if (driveService == null) return string.Empty;

                var file = await driveService.Files.Get(cloudFileId).ExecuteAsync();
                return file.WebViewLink ?? string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على رابط المشاركة من Google Drive");
                return string.Empty;
            }
        }

        // Helper methods
        private async Task<AppCloudStorageConfig?> GetConfigAsync(int configId)
        {
            return await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId && c.IsActive);
        }

        private async Task<DriveService?> CreateDriveServiceAsync(AppCloudStorageConfig config)
        {
            try
            {
                if (string.IsNullOrEmpty(config.AccessToken))
                {
                    return null;
                }

                var credential = GoogleCredential.FromAccessToken(config.AccessToken);

                return new DriveService(new BaseClientService.Initializer()
                {
                    HttpClientInitializer = credential,
                    ApplicationName = "AppDev Backup System"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء خدمة Google Drive");
                return null;
            }
        }

        private async Task<string> EnsureFolderExistsAsync(DriveService driveService, string folderPath)
        {
            var parts = folderPath.Trim('/').Split('/');
            var currentParentId = "root";

            foreach (var part in parts)
            {
                if (string.IsNullOrEmpty(part)) continue;

                var existingFolder = await FindFolderAsync(driveService, part, currentParentId);
                if (existingFolder != null)
                {
                    currentParentId = existingFolder.Id;
                }
                else
                {
                    var newFolder = await CreateFolderAsync(driveService, part, currentParentId);
                    currentParentId = newFolder.Id;
                }
            }

            return currentParentId;
        }

        private async Task<Google.Apis.Drive.v3.Data.File?> FindFolderAsync(DriveService driveService, string folderName, string parentId)
        {
            var listRequest = driveService.Files.List();
            listRequest.Q = $"name='{folderName}' and '{parentId}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false";
            listRequest.Fields = "files(id,name)";

            var result = await listRequest.ExecuteAsync();
            return result.Files.FirstOrDefault();
        }

        private async Task<Google.Apis.Drive.v3.Data.File> CreateFolderAsync(DriveService driveService, string folderName, string parentId)
        {
            var folderMetadata = new Google.Apis.Drive.v3.Data.File()
            {
                Name = folderName,
                MimeType = "application/vnd.google-apps.folder",
                Parents = new List<string> { parentId }
            };

            var request = driveService.Files.Create(folderMetadata);
            request.Fields = "id,name";
            return await request.ExecuteAsync();
        }

        private async Task<string> GetFolderIdAsync(DriveService driveService, string folderPath)
        {
            if (string.IsNullOrEmpty(folderPath) || folderPath == "/")
                return "root";

            return await EnsureFolderExistsAsync(driveService, folderPath);
        }

        private static string GetMimeType(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension switch
            {
                ".json" => "application/json",
                ".sql" => "application/sql",
                ".zip" => "application/zip",
                _ => "application/octet-stream"
            };
        }

        private static string FormatFileSize(long? bytes)
        {
            if (!bytes.HasValue || bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes.Value;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
