import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_app/app/data/providers/auth_provider.dart';
import 'package:mobile_app/app/data/services/auth_service.dart';
import 'package:mobile_app/app/routes/app_pages.dart';
import 'package:dio/dio.dart' as dio_pkg; // Import Dio with alias


class LoginController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final AuthProvider _authProvider = AuthProvider();
  final AuthService _authService = Get.find<AuthService>();

  var isLoading = false.obs;
  var errorMessage = ''.obs;

  Future<void> login() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final String email = emailController.text.trim();
      final String password = passwordController.text.trim();

      if (email.isEmpty || password.isEmpty) {
        errorMessage.value = 'Please enter both email and password.';
        return;
      }

      final response = await _authProvider.login(email, password);

      if (response.statusCode == 200) {
        final token = response.data['token'];
        final refreshToken = response.data['refreshToken'];
        await _authService.saveTokens(token, refreshToken);
        Get.offAllNamed(Routes.home); // Navigate to home on success
      } else {
        // Dio handles non-200 responses as DioException, so this else might not be reached for API errors
        // but for other non-200 responses (e.g., 404 from web server, not API)
        errorMessage.value = response.data['error']['message'] ?? 'Login failed. Please try again.';
      }
    } on dio_pkg.DioException catch (e) { // Use dio_pkg.DioException
      if (e.response != null) {
        errorMessage.value = e.response?.data['error']['message'] ?? 'Login failed. Please try again.';
      } else {
        errorMessage.value = 'An error occurred: ${e.message}';
      }
    } catch (e) {
      errorMessage.value = 'An unexpected error occurred: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }
}