using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AppDev.Server.Models.AppDevDB
{
    [Table("App_CloudStorageConfigs")]
    public class AppCloudStorageConfig
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ConfigId { get; set; }

        [Required]
        public int ProviderId { get; set; }

        [Required]
        [StringLength(100)]
        public string ConfigName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        // Authentication Settings
        [StringLength(1000)]
        public string? ClientId { get; set; }

        [StringLength(1000)]
        public string? ClientSecret { get; set; }

        [StringLength(2000)]
        public string? AccessToken { get; set; }

        [StringLength(2000)]
        public string? RefreshToken { get; set; }

        [StringLength(500)]
        public string? ApiKey { get; set; }

        [StringLength(200)]
        public string? Username { get; set; }

        [StringLength(500)]
        public string? EncryptedPassword { get; set; }

        // Storage Settings
        [StringLength(500)]
        public string? FolderPath { get; set; } = "/AppDev/Backups";

        [StringLength(100)]
        public string? FileNamePrefix { get; set; } = "AppDev_";

        public bool AutoUpload { get; set; } = false;

        public bool DeleteLocalAfterUpload { get; set; } = false;

        public int? RetentionDays { get; set; } = 30;

        public long? MaxStorageBytes { get; set; }

        // Notification Settings
        public bool NotifyOnSuccess { get; set; } = true;

        public bool NotifyOnFailure { get; set; } = true;

        [StringLength(500)]
        public string? NotificationEmail { get; set; }

        // Status
        [Required]
        public bool IsActive { get; set; } = true;

        public bool IsDefault { get; set; } = false;

        public DateTime? LastSyncDate { get; set; }

        [StringLength(500)]
        public string? LastSyncStatus { get; set; }

        [StringLength(1000)]
        public string? LastErrorMessage { get; set; }

        // Audit
        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime? LastModifiedDate { get; set; }

        [StringLength(450)]
        public string? CreatedByUserId { get; set; }

        [StringLength(450)]
        public string? LastModifiedByUserId { get; set; }

        // Navigation Properties
        [ForeignKey("ProviderId")]
        public virtual AppCloudStorageProvider Provider { get; set; } = null!;

        public virtual ICollection<AppBackupCloudUpload> BackupCloudUploads { get; set; } = new List<AppBackupCloudUpload>();
        public virtual ICollection<AppBackupSchedule> BackupSchedules { get; set; } = new List<AppBackupSchedule>();
    }
}
