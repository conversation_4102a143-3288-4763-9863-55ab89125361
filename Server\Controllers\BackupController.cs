using AppDev.Server.Data;
using AppDev.Server.Models;
using AppDev.Server.Models.AppDevDB;
using AppDev.Server.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using System.Reflection;
using System.IO.Compression;
using System.Text;
using System.Data.Common;

namespace AppDev.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class BackupController : ControllerBase
    {
        private readonly AppDevDBContext _dbContext;
        private readonly AppDevDBContext _context;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly ICloudStorageManager _cloudStorageManager;
        private readonly ILogger<BackupController> _logger;
        private readonly string _backupFolderPath;

        public BackupController(
            AppDevDBContext dbContext,
            IWebHostEnvironment webHostEnvironment,
            ICloudStorageManager cloudStorageManager,
            ILogger<BackupController> logger)
        {
            _dbContext = dbContext;
            _context = dbContext; // Alias for consistency
            _webHostEnvironment = webHostEnvironment;
            _cloudStorageManager = cloudStorageManager;
            _logger = logger;
            _backupFolderPath = Path.Combine(_webHostEnvironment.ContentRootPath, "Backups");

            if (!Directory.Exists(_backupFolderPath))
            {
                Directory.CreateDirectory(_backupFolderPath);
            }
        }

        [HttpGet("files")]
        public async Task<IActionResult> GetBackupFiles()
        {
            try
            {
                var files = Directory.GetFiles(_backupFolderPath)
                    .Select(f => new BackupInfo
                    {
                        FileName = Path.GetFileName(f),
                        CreatedAt = System.IO.File.GetCreationTime(f),
                        SizeBytes = new FileInfo(f).Length,
                        Type = Path.GetExtension(f).ToUpper().TrimStart('.')
                    })
                    .OrderByDescending(f => f.CreatedAt)
                    .ToList();

                return Ok(files);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في جلب ملفات النسخ الاحتياطية: {ex.Message}" });
            }
        }

        [HttpPost("create-json")]
        public async Task<IActionResult> CreateJsonBackup()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                var backupFileName = $"AppDevDB_json_{timestamp}.json";
                var backupFilePath = Path.Combine(_backupFolderPath, backupFileName);

                var dataToBackup = new Dictionary<string, object>();

                var tableNames = _dbContext.Model.GetEntityTypes()
                    .Select(t => t.GetTableName())
                    .Where(t => t != null)
                    .Distinct()
                    .ToList();

                foreach (var tableName in tableNames)
                {
                    if (tableName != null)
                    {
                        try
                        {
                            var entityType = _dbContext.Model.GetEntityTypes().FirstOrDefault(e => e.GetTableName() == tableName);
                            if (entityType != null)
                            {
                                var data = await GetTableData(entityType.ClrType);
                                dataToBackup[tableName] = data;
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"خطأ في نسخ الجدول {tableName}: {ex.Message}");
                        }
                    }
                }

                var json = JsonSerializer.Serialize(dataToBackup, new JsonSerializerOptions { WriteIndented = true });
                await System.IO.File.WriteAllTextAsync(backupFilePath, json);

                // ضغط الملف
                var zipFileName = $"AppDevDB_json_{timestamp}.zip";
                var zipFilePath = Path.Combine(_backupFolderPath, zipFileName);

                using (var archive = ZipFile.Open(zipFilePath, ZipArchiveMode.Create))
                {
                    var entry = archive.CreateEntry(Path.GetFileName(backupFileName), CompressionLevel.Optimal);
                    using (var entryStream = entry.Open())
                    using (var fileStream = System.IO.File.OpenRead(backupFilePath))
                    {
                        await fileStream.CopyToAsync(entryStream);
                    }
                }

                // حذف الملف الأصلي
                System.IO.File.Delete(backupFilePath);

                return Ok(new BackupResult { Success = true, BackupPath = zipFilePath, Message = "تم إنشاء النسخة الاحتياطية JSON بنجاح" });
            }
            catch (Exception ex)
            {
                return Ok(new BackupResult { Success = false, Message = $"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}" });
            }
        }

        [HttpPost("create-sql")]
        public async Task<IActionResult> CreateSqlBackup()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                var backupFileName = $"AppDevDB_sql_{timestamp}.sql";
                var backupFilePath = Path.Combine(_backupFolderPath, backupFileName);

                var sqlContent = new StringBuilder();

                // إضافة header للملف
                sqlContent.AppendLine("-- MySQL dump generated by AppDev Backup System");
                sqlContent.AppendLine($"-- Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                sqlContent.AppendLine("-- ------------------------------------------------------");
                sqlContent.AppendLine();

                sqlContent.AppendLine("SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0;");
                sqlContent.AppendLine("SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0;");
                sqlContent.AppendLine("SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO';");
                sqlContent.AppendLine();

                var tableNames = _dbContext.Model.GetEntityTypes()
                    .Select(t => t.GetTableName())
                    .Where(t => t != null)
                    .Distinct()
                    .ToList();

                foreach (var tableName in tableNames)
                {
                    if (tableName != null)
                    {
                        try
                        {
                            sqlContent.AppendLine($"-- Table structure for table `{tableName}`");
                            sqlContent.AppendLine($"DROP TABLE IF EXISTS `{tableName}`;");

                            var createTableSql = await GetCreateTableStatement(tableName);
                            if (!string.IsNullOrEmpty(createTableSql))
                            {
                                sqlContent.AppendLine(createTableSql);
                                sqlContent.AppendLine();
                            }

                            sqlContent.AppendLine($"-- Dumping data for table `{tableName}`");
                            var insertStatements = await GetInsertStatements(tableName);
                            if (insertStatements.Any())
                            {
                                sqlContent.AppendLine($"LOCK TABLES `{tableName}` WRITE;");
                                sqlContent.AppendLine($"/*!40000 ALTER TABLE `{tableName}` DISABLE KEYS */;");

                                foreach (var insert in insertStatements)
                                {
                                    sqlContent.AppendLine(insert);
                                }

                                sqlContent.AppendLine($"/*!40000 ALTER TABLE `{tableName}` ENABLE KEYS */;");
                                sqlContent.AppendLine("UNLOCK TABLES;");
                                sqlContent.AppendLine();
                            }
                        }
                        catch (Exception ex)
                        {
                            sqlContent.AppendLine($"-- Error processing table {tableName}: {ex.Message}");
                        }
                    }
                }

                sqlContent.AppendLine("SET SQL_MODE=@OLD_SQL_MODE;");
                sqlContent.AppendLine("SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS;");
                sqlContent.AppendLine("SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS;");

                await System.IO.File.WriteAllTextAsync(backupFilePath, sqlContent.ToString());

                // ضغط الملف
                var zipFileName = $"AppDevDB_sql_{timestamp}.zip";
                var zipFilePath = Path.Combine(_backupFolderPath, zipFileName);

                using (var archive = ZipFile.Open(zipFilePath, ZipArchiveMode.Create))
                {
                    var entry = archive.CreateEntry(Path.GetFileName(backupFileName), CompressionLevel.Optimal);
                    using (var entryStream = entry.Open())
                    using (var fileStream = System.IO.File.OpenRead(backupFilePath))
                    {
                        await fileStream.CopyToAsync(entryStream);
                    }
                }

                System.IO.File.Delete(backupFilePath);

                return Ok(new BackupResult { Success = true, BackupPath = zipFilePath, Message = "تم إنشاء النسخة الاحتياطية SQL بنجاح" });
            }
            catch (Exception ex)
            {
                return Ok(new BackupResult { Success = false, Message = $"خطأ في العملية: {ex.Message}" });
            }
        }

        [HttpPost("validate")]
        public async Task<IActionResult> ValidateBackup([FromBody] string fileName)
        {
            try
            {
                var filePath = Path.Combine(_backupFolderPath, fileName);
                if (!System.IO.File.Exists(filePath))
                {
                    return Ok(new BackupValidationResult { IsValid = false, Message = "الملف غير موجود" });
                }

                var fileInfo = new FileInfo(filePath);
                var result = new BackupValidationResult
                {
                    SizeBytes = fileInfo.Length,
                    BackupDate = fileInfo.CreationTime
                };

                if (fileName.EndsWith(".zip"))
                {
                    result.BackupType = "ZIP";
                    using var archive = ZipFile.OpenRead(filePath);
                    var entries = archive.Entries.Where(e => e.Name.EndsWith(".sql") || e.Name.EndsWith(".json")).ToList();

                    if (entries.Any())
                    {
                        result.IsValid = true;
                        result.Message = $"نسخة احتياطية صالحة تحتوي على {entries.Count} ملف";
                        result.Tables = entries.Select(e => e.Name).ToList();
                    }
                    else
                    {
                        result.IsValid = false;
                        result.Message = "الملف المضغوط لا يحتوي على ملفات نسخ احتياطية صالحة";
                    }
                }
                else
                {
                    result.IsValid = false;
                    result.Message = "نوع ملف غير مدعوم";
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                return Ok(new BackupValidationResult { IsValid = false, Message = $"خطأ في فحص الملف: {ex.Message}" });
            }
        }

        [HttpGet("download/{fileName}")]
        public async Task<IActionResult> DownloadBackup(string fileName)
        {
            try
            {
                var filePath = Path.Combine(_backupFolderPath, fileName);
                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound(new { Success = false, Message = "الملف غير موجود" });
                }

                var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
                return File(fileBytes, "application/octet-stream", fileName);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في تحميل الملف: {ex.Message}" });
            }
        }

        [HttpDelete("delete/{fileName}")]
        public async Task<IActionResult> DeleteBackup(string fileName)
        {
            try
            {
                var filePath = Path.Combine(_backupFolderPath, fileName);
                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound(new { Success = false, Message = "الملف غير موجود" });
                }

                System.IO.File.Delete(filePath);
                return Ok(new { Success = true, Message = "تم حذف النسخة الاحتياطية بنجاح" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في حذف الملف: {ex.Message}" });
            }
        }

        // Cloud Storage APIs
        [HttpGet("cloud/providers")]
        public async Task<IActionResult> GetCloudProviders()
        {
            try
            {
                var providers = await _cloudStorageManager.GetProvidersAsync();
                return Ok(providers);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في جلب مقدمي الخدمة السحابية: {ex.Message}" });
            }
        }

        [HttpGet("cloud/configs")]
        public async Task<IActionResult> GetCloudConfigs()
        {
            try
            {
                var configs = await _cloudStorageManager.GetConfigsAsync();
                return Ok(configs);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في جلب إعدادات التخزين السحابي: {ex.Message}" });
            }
        }

        [HttpGet("cloud/configs/{configId}")]
        public async Task<IActionResult> GetCloudConfig(int configId)
        {
            try
            {
                var config = await _cloudStorageManager.GetConfigAsync(configId);
                if (config == null)
                {
                    return NotFound(new { Success = false, Message = "إعدادات التخزين السحابي غير موجودة" });
                }
                return Ok(config);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في جلب إعدادات التخزين السحابي: {ex.Message}" });
            }
        }

        [HttpPost("cloud/configs")]
        public async Task<IActionResult> CreateCloudConfig([FromBody] CreateCloudStorageConfigRequest request)
        {
            try
            {
                var userId = User.Identity?.Name ?? "system";
                var config = await _cloudStorageManager.CreateConfigAsync(request, userId);
                return Ok(new { Success = true, Config = config, Message = "تم إنشاء إعدادات التخزين السحابي بنجاح" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في إنشاء إعدادات التخزين السحابي: {ex.Message}" });
            }
        }

        [HttpPut("cloud/configs")]
        public async Task<IActionResult> UpdateCloudConfig([FromBody] UpdateCloudStorageConfigRequest request)
        {
            try
            {
                var userId = User.Identity?.Name ?? "system";
                var config = await _cloudStorageManager.UpdateConfigAsync(request, userId);
                if (config == null)
                {
                    return NotFound(new { Success = false, Message = "إعدادات التخزين السحابي غير موجودة" });
                }
                return Ok(new { Success = true, Config = config, Message = "تم تحديث إعدادات التخزين السحابي بنجاح" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في تحديث إعدادات التخزين السحابي: {ex.Message}" });
            }
        }

        [HttpDelete("cloud/configs/{configId}")]
        public async Task<IActionResult> DeleteCloudConfig(int configId)
        {
            try
            {
                var success = await _cloudStorageManager.DeleteConfigAsync(configId);
                if (!success)
                {
                    return NotFound(new { Success = false, Message = "إعدادات التخزين السحابي غير موجودة" });
                }
                return Ok(new { Success = true, Message = "تم حذف إعدادات التخزين السحابي بنجاح" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في حذف إعدادات التخزين السحابي: {ex.Message}" });
            }
        }

        [HttpPost("cloud/test/{configId}")]
        public async Task<IActionResult> TestCloudConnection(int configId)
        {
            try
            {
                var result = await _cloudStorageManager.TestConnectionAsync(configId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في اختبار الاتصال: {ex.Message}" });
            }
        }

        [HttpPost("cloud/upload")]
        public async Task<IActionResult> UploadToCloud([FromBody] CloudUploadRequest request)
        {
            try
            {
                var result = await _cloudStorageManager.UploadFileAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في رفع الملف: {ex.Message}" });
            }
        }

        [HttpPost("cloud/upload-file")]
        public async Task<IActionResult> UploadFileToCloud([FromForm] IFormFile file, [FromForm] int ConfigId,
            [FromForm] string BackupType = "Manual", [FromForm] string CustomFileName = "",
            [FromForm] string Tags = "", [FromForm] string Notes = "",
            [FromForm] bool DeleteLocalAfterUpload = false)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { Success = false, Message = "لم يتم اختيار ملف" });
                }

                // حفظ الملف مؤقتاً
                var tempFileName = $"temp_{Guid.NewGuid()}_{file.FileName}";
                var tempFilePath = Path.Combine(_backupFolderPath, tempFileName);

                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                try
                {
                    var uploadRequest = new CloudUploadRequest
                    {
                        ConfigId = ConfigId,
                        LocalFilePath = tempFilePath,
                        BackupType = BackupType,
                        CustomFileName = string.IsNullOrEmpty(CustomFileName) ? file.FileName : CustomFileName,
                        Tags = Tags,
                        Notes = Notes,
                        DeleteLocalAfterUpload = false // لا نحذف الملف المؤقت هنا
                    };

                    var result = await _cloudStorageManager.UploadFileAsync(uploadRequest);

                    // حذف الملف المؤقت
                    if (System.IO.File.Exists(tempFilePath))
                    {
                        System.IO.File.Delete(tempFilePath);
                    }

                    return Ok(result);
                }
                catch
                {
                    // حذف الملف المؤقت في حالة الخطأ
                    if (System.IO.File.Exists(tempFilePath))
                    {
                        System.IO.File.Delete(tempFilePath);
                    }
                    throw;
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في رفع الملف: {ex.Message}" });
            }
        }

        [HttpGet("cloud/quota/{configId}")]
        public async Task<IActionResult> GetCloudQuota(int configId)
        {
            try
            {
                var quota = await _cloudStorageManager.GetQuotaInfoAsync(configId);
                return Ok(quota);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في جلب معلومات المساحة: {ex.Message}" });
            }
        }

        [HttpGet("cloud/files/{configId}")]
        public async Task<IActionResult> ListCloudFiles(int configId, [FromQuery] string? folderPath = null)
        {
            try
            {
                var files = await _cloudStorageManager.ListFilesAsync(configId, folderPath);
                return Ok(files);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في جلب قائمة الملفات: {ex.Message}" });
            }
        }

        [HttpGet("cloud/uploads")]
        public async Task<IActionResult> GetCloudUploads()
        {
            try
            {
                var uploads = await _dbContext.AppBackupCloudUploads
                    .Include(u => u.Config)
                    .Include(u => u.Provider)
                    .OrderByDescending(u => u.CreatedDate)
                    .Take(50)
                    .Select(u => new BackupCloudUploadDto
                    {
                        UploadId = u.UploadId,
                        LocalFileName = u.LocalFileName,
                        CloudFileName = u.CloudFileName,
                        CloudFileUrl = u.CloudFileUrl,
                        BackupType = u.BackupType,
                        FileSizeBytes = u.FileSizeBytes,
                        UploadStatus = u.UploadStatus,
                        UploadProgress = u.UploadProgress,
                        UploadStartTime = u.UploadStartTime,
                        UploadEndTime = u.UploadEndTime,
                        UploadDuration = u.UploadDuration,
                        ErrorMessage = u.ErrorMessage,
                        CreatedDate = u.CreatedDate,
                        ProviderName = u.Provider.ProviderName,
                        ConfigName = u.Config.ConfigName
                    })
                    .ToListAsync();

                return Ok(uploads);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في جلب سجل الرفع السحابي: {ex.Message}" });
            }
        }

        [HttpPost("upload-existing-to-cloud")]
        public async Task<IActionResult> UploadExistingBackupToCloud([FromBody] UploadExistingBackupRequest request)
        {
            try
            {
                var filePath = Path.Combine(_backupFolderPath, request.FileName);
                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound(new { Success = false, Message = "الملف غير موجود" });
                }

                var uploadRequest = new CloudUploadRequest
                {
                    ConfigId = request.ConfigId,
                    LocalFilePath = filePath,
                    BackupType = request.BackupType ?? "Manual",
                    CustomFileName = request.CustomFileName,
                    Tags = request.Tags,
                    Notes = request.Notes,
                    DeleteLocalAfterUpload = request.DeleteLocalAfterUpload
                };

                var result = await _cloudStorageManager.UploadFileAsync(uploadRequest);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في رفع الملف: {ex.Message}" });
            }
        }

        [HttpPost("create-backup-and-upload")]
        public async Task<IActionResult> CreateBackupAndUpload([FromBody] CreateBackupAndUploadRequest request)
        {
            try
            {
                // Create backup first
                BackupResult backupResult;
                if (request.BackupType?.ToLower() == "sql")
                {
                    var sqlResult = await CreateSqlBackup();
                    backupResult = new BackupResult
                    {
                        Success = sqlResult is OkObjectResult,
                        BackupPath = sqlResult is OkObjectResult okResult ?
                            ((dynamic)okResult.Value).FilePath : null,
                        Message = sqlResult is OkObjectResult okResult2 ?
                            ((dynamic)okResult2.Value).Message : "فشل في إنشاء النسخة الاحتياطية"
                    };
                }
                else
                {
                    var jsonResult = await CreateJsonBackup();
                    backupResult = new BackupResult
                    {
                        Success = jsonResult is OkObjectResult,
                        BackupPath = jsonResult is OkObjectResult okResult ?
                            ((dynamic)okResult.Value).FilePath : null,
                        Message = jsonResult is OkObjectResult okResult2 ?
                            ((dynamic)okResult2.Value).Message : "فشل في إنشاء النسخة الاحتياطية"
                    };
                }

                if (!backupResult.Success)
                {
                    return BadRequest(backupResult);
                }

                // Upload to cloud if config provided
                if (request.CloudConfigId.HasValue && backupResult.Success && !string.IsNullOrEmpty(backupResult.BackupPath))
                {
                    var uploadRequest = new CloudUploadRequest
                    {
                        ConfigId = request.CloudConfigId.Value,
                        LocalFilePath = backupResult.BackupPath,
                        BackupType = request.BackupType ?? "Manual",
                        CustomFileName = request.CustomFileName,
                        Tags = request.Tags,
                        Notes = request.Notes,
                        DeleteLocalAfterUpload = request.DeleteLocalAfterUpload ?? false
                    };

                    var uploadResult = await _cloudStorageManager.UploadFileAsync(uploadRequest);

                    return Ok(new
                    {
                        Success = true,
                        Message = "تم إنشاء النسخة الاحتياطية ورفعها للسحابة بنجاح",
                        BackupResult = backupResult,
                        UploadResult = uploadResult
                    });
                }

                return Ok(new
                {
                    Success = true,
                    Message = "تم إنشاء النسخة الاحتياطية بنجاح",
                    BackupResult = backupResult
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { Success = false, Message = $"خطأ في إنشاء ورفع النسخة الاحتياطية: {ex.Message}" });
            }
        }

        // الوظائف المساعدة
        private async Task<List<object>> GetTableData(Type entityType)
        {
            var method = typeof(BackupController).GetMethod("GetTableDataGeneric", BindingFlags.NonPublic | BindingFlags.Instance)?.MakeGenericMethod(entityType);
            if (method != null)
            {
                var task = (Task<List<object>>)method.Invoke(this, new object[] { });
                return await task;
            }
            return new List<object>();
        }

        private async Task<List<object>> GetTableDataGeneric<T>() where T : class
        {
            var data = await _dbContext.Set<T>().ToListAsync();
            return data.Cast<object>().ToList();
        }

        private async Task<string> GetCreateTableStatement(string tableName)
        {
            try
            {
                using var connection = _dbContext.Database.GetDbConnection();
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = $"SHOW CREATE TABLE `{tableName}`";
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    return reader.GetString(1) + ";";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على بنية الجدول {tableName}: {ex.Message}");
            }

            return string.Empty;
        }

        private async Task<List<string>> GetInsertStatements(string tableName)
        {
            var insertStatements = new List<string>();

            try
            {
                using var connection = _dbContext.Database.GetDbConnection();
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = $"SELECT * FROM `{tableName}`";
                using var reader = await command.ExecuteReaderAsync();

                var columnNames = new List<string>();
                for (int i = 0; i < reader.FieldCount; i++)
                {
                    columnNames.Add($"`{reader.GetName(i)}`");
                }

                var values = new List<string>();
                while (await reader.ReadAsync())
                {
                    var rowValues = new List<string>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        if (reader.IsDBNull(i))
                        {
                            rowValues.Add("NULL");
                        }
                        else
                        {
                            var value = reader.GetValue(i);
                            if (value is string || value is DateTime)
                            {
                                rowValues.Add($"'{value.ToString()?.Replace("'", "''")}'");
                            }
                            else
                            {
                                rowValues.Add(value.ToString() ?? "NULL");
                            }
                        }
                    }
                    values.Add($"({string.Join(",", rowValues)})");
                }

                if (values.Any())
                {
                    var columnsString = string.Join(",", columnNames);
                    const int batchSize = 100;
                    for (int i = 0; i < values.Count; i += batchSize)
                    {
                        var batch = values.Skip(i).Take(batchSize);
                        var valuesString = string.Join(",", batch);
                        insertStatements.Add($"INSERT INTO `{tableName}` ({columnsString}) VALUES {valuesString};");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الحصول على بيانات الجدول {tableName}: {ex.Message}");
            }

            return insertStatements;
        }

        // Instant Backup API
        [HttpPost("instant")]
        public async Task<IActionResult> CreateInstantBackup([FromBody] InstantBackupRequest request)
        {
            try
            {
                BackupResult backupResult;

                // Create backup based on type
                if (request.BackupType == "JSON")
                {
                    backupResult = await CreateJsonBackupInternal();
                }
                else if (request.BackupType == "SQL")
                {
                    backupResult = await CreateSqlBackupInternal();
                }
                else // Both
                {
                    var jsonResult = await CreateJsonBackupInternal();
                    var sqlResult = await CreateSqlBackupInternal();

                    backupResult = new BackupResult
                    {
                        Success = jsonResult.Success && sqlResult.Success,
                        BackupPath = jsonResult.Success ? jsonResult.BackupPath : sqlResult.BackupPath,
                        Message = $"JSON: {jsonResult.Message}, SQL: {sqlResult.Message}"
                    };
                }

                if (!backupResult.Success)
                {
                    return StatusCode(500, new { Message = backupResult.Message });
                }

                // Upload to cloud if requested
                if (request.UploadToCloud && request.CloudConfigId.HasValue && !string.IsNullOrEmpty(backupResult.BackupPath))
                {
                    var uploadRequest = new CloudUploadRequest
                    {
                        ConfigId = request.CloudConfigId.Value,
                        LocalFilePath = backupResult.BackupPath,
                        BackupType = "Instant",
                        Tags = "instant,manual",
                        Notes = request.Notes ?? "نسخة احتياطية فورية",
                        DeleteLocalAfterUpload = request.DeleteLocalAfterUpload
                    };

                    var uploadResult = await _cloudStorageManager.UploadFileAsync(uploadRequest);

                    if (uploadResult.Success)
                    {
                        backupResult.Message += " - تم الرفع للسحابة بنجاح";
                    }
                    else
                    {
                        backupResult.Message += $" - فشل الرفع للسحابة: {uploadResult.Message}";
                    }
                }

                return Ok(new
                {
                    Message = backupResult.Message,
                    BackupPath = backupResult.BackupPath,
                    Success = backupResult.Success
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء النسخة الاحتياطية الفورية");
                return StatusCode(500, new { Message = "خطأ في إنشاء النسخة الاحتياطية الفورية", Error = ex.Message });
            }
        }

        // Dashboard API
        [HttpGet("dashboard")]
        public async Task<IActionResult> GetDashboardData()
        {
            try
            {
                var dashboardData = new BackupDashboardData();

                // Count local backups
                var backupFiles = Directory.GetFiles(_backupFolderPath, "*.zip");
                dashboardData.LocalBackupsCount = backupFiles.Length;
                dashboardData.LocalBackupsSizeGB = backupFiles.Sum(f => new FileInfo(f).Length) / (1024.0 * 1024.0 * 1024.0);

                // Count cloud backups
                var cloudBackups = await _context.AppBackupCloudUploads.CountAsync();
                dashboardData.CloudBackupsCount = cloudBackups;

                var cloudBackupsSize = await _context.AppBackupCloudUploads
                    .SumAsync(b => b.FileSizeBytes);
                dashboardData.CloudBackupsSizeGB = cloudBackupsSize / (1024.0 * 1024.0 * 1024.0);

                // Count schedules
                var totalSchedules = await _context.AppBackupSchedules.CountAsync();
                var activeSchedules = await _context.AppBackupSchedules.CountAsync(s => s.IsActive);
                dashboardData.TotalSchedulesCount = totalSchedules;
                dashboardData.ActiveSchedulesCount = activeSchedules;

                // Count cloud configs
                var totalCloudConfigs = await _context.AppCloudStorageConfigs.CountAsync();
                var activeCloudConfigs = await _context.AppCloudStorageConfigs.CountAsync(c => c.IsActive);
                dashboardData.CloudConfigsCount = totalCloudConfigs;
                dashboardData.ActiveCloudConfigsCount = activeCloudConfigs;

                // Recent backups (combine local and cloud)
                var recentBackups = new List<RecentBackupDto>();

                // Add recent local backups
                var recentLocalFiles = backupFiles
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .Take(10)
                    .Select(f => new RecentBackupDto
                    {
                        FileName = f.Name,
                        CreatedDate = f.CreationTime,
                        FileSizeGB = f.Length / (1024.0 * 1024.0 * 1024.0),
                        IsCloudBackup = false,
                        BackupType = "Local"
                    });

                recentBackups.AddRange(recentLocalFiles);

                // Add recent cloud backups
                var recentCloudBackups = await _context.AppBackupCloudUploads
                    .OrderByDescending(b => b.CreatedDate)
                    .Take(10)
                    .Select(b => new RecentBackupDto
                    {
                        FileName = b.CloudFileName ?? "Unknown",
                        CreatedDate = b.CreatedDate,
                        FileSizeGB = b.FileSizeBytes / (1024.0 * 1024.0 * 1024.0),
                        IsCloudBackup = true,
                        BackupType = b.BackupType ?? "Cloud"
                    })
                    .ToListAsync();

                recentBackups.AddRange(recentCloudBackups);

                // Sort all recent backups by date and take top 10
                dashboardData.RecentBackups = recentBackups
                    .OrderByDescending(b => b.CreatedDate)
                    .Take(10)
                    .ToList();

                // Schedule status
                dashboardData.ScheduleStatus = await _context.AppBackupSchedules
                    .OrderByDescending(s => s.LastRunTime)
                    .Take(10)
                    .Select(s => new ScheduleStatusDto
                    {
                        ScheduleName = s.ScheduleName,
                        IsActive = s.IsActive,
                        LastRunTime = s.LastRunTime,
                        LastRunStatus = s.LastRunStatus,
                        NextRunTime = s.NextRunTime
                    })
                    .ToListAsync();

                return Ok(dashboardData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب بيانات لوحة التحكم");
                return StatusCode(500, new { Message = "خطأ في جلب بيانات لوحة التحكم", Error = ex.Message });
            }
        }

        // Backup Schedule Management
        [HttpGet("schedules")]
        public async Task<IActionResult> GetBackupSchedules()
        {
            try
            {
                var schedules = await _context.AppBackupSchedules
                    .Include(s => s.CloudConfig)
                    .ThenInclude(c => c!.Provider)
                    .OrderBy(s => s.ScheduleName)
                    .Select(s => new
                    {
                        s.ScheduleId,
                        s.ScheduleName,
                        s.Description,
                        s.BackupType,
                        s.ScheduleType,
                        s.CronExpression,
                        s.UploadToCloud,
                        CloudConfigName = s.CloudConfig != null ? s.CloudConfig.ConfigName : null,
                        ProviderName = s.CloudConfig != null && s.CloudConfig.Provider != null ? s.CloudConfig.Provider.ProviderName : null,
                        s.DeleteLocalAfterUpload,
                        s.LocalRetentionDays,
                        s.CloudRetentionDays,
                        s.NotifyOnSuccess,
                        s.NotifyOnFailure,
                        s.NotificationEmail,
                        s.IsActive,
                        s.LastRunTime,
                        s.NextRunTime,
                        s.LastRunStatus,
                        s.LastRunMessage,
                        s.SuccessfulRuns,
                        s.FailedRuns,
                        s.CreatedDate
                    })
                    .ToListAsync();

                return Ok(schedules);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جداول النسخ الاحتياطي");
                return StatusCode(500, new { Message = "خطأ في جلب جداول النسخ الاحتياطي", Error = ex.Message });
            }
        }

        [HttpGet("schedules/{id}")]
        public async Task<IActionResult> GetBackupSchedule(int id)
        {
            try
            {
                var schedule = await _context.AppBackupSchedules
                    .Include(s => s.CloudConfig)
                    .ThenInclude(c => c!.Provider)
                    .FirstOrDefaultAsync(s => s.ScheduleId == id);

                if (schedule == null)
                {
                    return NotFound(new { Message = "جدول النسخ الاحتياطي غير موجود" });
                }

                return Ok(schedule);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جدول النسخ الاحتياطي");
                return StatusCode(500, new { Message = "خطأ في جلب جدول النسخ الاحتياطي", Error = ex.Message });
            }
        }

        [HttpPost("schedules")]
        public async Task<IActionResult> CreateBackupSchedule([FromBody] CreateBackupScheduleRequest request)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(request.ScheduleName))
                {
                    return BadRequest(new { Message = "اسم الجدول مطلوب" });
                }

                // التحقق من عدم وجود جدول بنفس الاسم
                var existingSchedule = await _context.AppBackupSchedules
                    .FirstOrDefaultAsync(s => s.ScheduleName == request.ScheduleName);

                if (existingSchedule != null)
                {
                    return BadRequest(new { Message = "يوجد جدول بنفس الاسم مسبقاً" });
                }

                // التحقق من صحة إعدادات السحابة إذا كانت مفعلة
                if (request.UploadToCloud && request.CloudConfigId.HasValue)
                {
                    var cloudConfig = await _context.AppCloudStorageConfigs
                        .FirstOrDefaultAsync(c => c.ConfigId == request.CloudConfigId.Value && c.IsActive);

                    if (cloudConfig == null)
                    {
                        return BadRequest(new { Message = "إعدادات التخزين السحابي غير صحيحة أو غير نشطة" });
                    }
                }

                var schedule = new AppBackupSchedule
                {
                    ScheduleName = request.ScheduleName,
                    Description = request.Description,
                    BackupType = request.BackupType,
                    ScheduleType = request.ScheduleType,
                    CronExpression = request.CronExpression,
                    UploadToCloud = request.UploadToCloud,
                    CloudConfigId = request.CloudConfigId,
                    DeleteLocalAfterUpload = request.DeleteLocalAfterUpload,
                    LocalRetentionDays = request.LocalRetentionDays,
                    CloudRetentionDays = request.CloudRetentionDays,
                    MaxLocalBackups = request.MaxLocalBackups,
                    MaxCloudBackups = request.MaxCloudBackups,
                    NotifyOnSuccess = request.NotifyOnSuccess,
                    NotifyOnFailure = request.NotifyOnFailure,
                    NotificationEmail = request.NotificationEmail,
                    CompressBackups = request.CompressBackups,
                    EncryptBackups = request.EncryptBackups,
                    VerifyIntegrity = request.VerifyIntegrity,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                };

                _context.AppBackupSchedules.Add(schedule);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"تم إنشاء جدول النسخ الاحتياطي: {schedule.ScheduleName}");

                return Ok(new { Message = "تم إنشاء جدول النسخ الاحتياطي بنجاح", ScheduleId = schedule.ScheduleId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء جدول النسخ الاحتياطي");
                return StatusCode(500, new { Message = "خطأ في إنشاء جدول النسخ الاحتياطي", Error = ex.Message });
            }
        }

        [HttpPut("schedules")]
        public async Task<IActionResult> UpdateBackupSchedule([FromBody] UpdateBackupScheduleRequest request)
        {
            try
            {
                var schedule = await _context.AppBackupSchedules
                    .FirstOrDefaultAsync(s => s.ScheduleId == request.ScheduleId);

                if (schedule == null)
                {
                    return NotFound(new { Message = "جدول النسخ الاحتياطي غير موجود" });
                }

                // التحقق من عدم وجود جدول آخر بنفس الاسم
                var existingSchedule = await _context.AppBackupSchedules
                    .FirstOrDefaultAsync(s => s.ScheduleName == request.ScheduleName && s.ScheduleId != request.ScheduleId);

                if (existingSchedule != null)
                {
                    return BadRequest(new { Message = "يوجد جدول آخر بنفس الاسم" });
                }

                // التحقق من صحة إعدادات السحابة إذا كانت مفعلة
                if (request.UploadToCloud && request.CloudConfigId.HasValue)
                {
                    var cloudConfig = await _context.AppCloudStorageConfigs
                        .FirstOrDefaultAsync(c => c.ConfigId == request.CloudConfigId.Value && c.IsActive);

                    if (cloudConfig == null)
                    {
                        return BadRequest(new { Message = "إعدادات التخزين السحابي غير صحيحة أو غير نشطة" });
                    }
                }

                // تحديث البيانات
                schedule.ScheduleName = request.ScheduleName;
                schedule.Description = request.Description;
                schedule.BackupType = request.BackupType;
                schedule.ScheduleType = request.ScheduleType;
                schedule.CronExpression = request.CronExpression;
                schedule.UploadToCloud = request.UploadToCloud;
                schedule.CloudConfigId = request.CloudConfigId;
                schedule.DeleteLocalAfterUpload = request.DeleteLocalAfterUpload;
                schedule.LocalRetentionDays = request.LocalRetentionDays;
                schedule.CloudRetentionDays = request.CloudRetentionDays;
                schedule.MaxLocalBackups = request.MaxLocalBackups;
                schedule.MaxCloudBackups = request.MaxCloudBackups;
                schedule.NotifyOnSuccess = request.NotifyOnSuccess;
                schedule.NotifyOnFailure = request.NotifyOnFailure;
                schedule.NotificationEmail = request.NotificationEmail;
                schedule.CompressBackups = request.CompressBackups;
                schedule.EncryptBackups = request.EncryptBackups;
                schedule.VerifyIntegrity = request.VerifyIntegrity;
                schedule.LastModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation($"تم تحديث جدول النسخ الاحتياطي: {schedule.ScheduleName}");

                return Ok(new { Message = "تم تحديث جدول النسخ الاحتياطي بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث جدول النسخ الاحتياطي");
                return StatusCode(500, new { Message = "خطأ في تحديث جدول النسخ الاحتياطي", Error = ex.Message });
            }
        }

        [HttpDelete("schedules/{id}")]
        public async Task<IActionResult> DeleteBackupSchedule(int id)
        {
            try
            {
                var schedule = await _context.AppBackupSchedules
                    .FirstOrDefaultAsync(s => s.ScheduleId == id);

                if (schedule == null)
                {
                    return NotFound(new { Message = "جدول النسخ الاحتياطي غير موجود" });
                }

                _context.AppBackupSchedules.Remove(schedule);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"تم حذف جدول النسخ الاحتياطي: {schedule.ScheduleName}");

                return Ok(new { Message = "تم حذف جدول النسخ الاحتياطي بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف جدول النسخ الاحتياطي");
                return StatusCode(500, new { Message = "خطأ في حذف جدول النسخ الاحتياطي", Error = ex.Message });
            }
        }

        [HttpPost("schedules/{id}/toggle")]
        public async Task<IActionResult> ToggleBackupSchedule(int id)
        {
            try
            {
                var schedule = await _context.AppBackupSchedules
                    .FirstOrDefaultAsync(s => s.ScheduleId == id);

                if (schedule == null)
                {
                    return NotFound(new { Message = "جدول النسخ الاحتياطي غير موجود" });
                }

                schedule.IsActive = !schedule.IsActive;
                schedule.LastModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var status = schedule.IsActive ? "تفعيل" : "إلغاء تفعيل";
                _logger.LogInformation($"تم {status} جدول النسخ الاحتياطي: {schedule.ScheduleName}");

                return Ok(new
                {
                    Message = $"تم {status} جدول النسخ الاحتياطي بنجاح",
                    IsActive = schedule.IsActive
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير حالة جدول النسخ الاحتياطي");
                return StatusCode(500, new { Message = "خطأ في تغيير حالة جدول النسخ الاحتياطي", Error = ex.Message });
            }
        }

        [HttpPost("schedules/{id}/run")]
        public async Task<IActionResult> RunBackupScheduleNow(int id)
        {
            try
            {
                var schedule = await _context.AppBackupSchedules
                    .Include(s => s.CloudConfig)
                    .FirstOrDefaultAsync(s => s.ScheduleId == id);

                if (schedule == null)
                {
                    return NotFound(new { Message = "جدول النسخ الاحتياطي غير موجود" });
                }

                if (!schedule.IsActive)
                {
                    return BadRequest(new { Message = "جدول النسخ الاحتياطي غير نشط" });
                }

                // تشغيل النسخ الاحتياطي فوراً
                var backupResult = await ExecuteScheduledBackup(schedule);

                if (backupResult.Success)
                {
                    return Ok(new
                    {
                        Message = "تم تشغيل النسخ الاحتياطي بنجاح",
                        BackupPath = backupResult.BackupPath
                    });
                }
                else
                {
                    return StatusCode(500, new
                    {
                        Message = "فشل في تشغيل النسخ الاحتياطي",
                        Error = backupResult.Message
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تشغيل النسخ الاحتياطي المجدول");
                return StatusCode(500, new { Message = "خطأ في تشغيل النسخ الاحتياطي المجدول", Error = ex.Message });
            }
        }

        private async Task<BackupResult> ExecuteScheduledBackup(AppBackupSchedule schedule)
        {
            try
            {
                var startTime = DateTime.UtcNow;

                // تحديث حالة البداية
                schedule.LastRunTime = startTime;
                schedule.LastRunStatus = "InProgress";
                schedule.LastRunMessage = "جاري تشغيل النسخ الاحتياطي...";
                await _context.SaveChangesAsync();

                var backupFolderPath = Path.Combine(_webHostEnvironment.ContentRootPath, "Backups");
                if (!Directory.Exists(backupFolderPath))
                {
                    Directory.CreateDirectory(backupFolderPath);
                }

                BackupResult backupResult;

                // إنشاء النسخة الاحتياطية حسب النوع
                if (schedule.BackupType == "JSON")
                {
                    backupResult = await CreateJsonBackupInternal();
                }
                else if (schedule.BackupType == "SQL")
                {
                    backupResult = await CreateSqlBackupInternal();
                }
                else
                {
                    // Both - إنشاء كلا النوعين
                    var jsonResult = await CreateJsonBackupInternal();
                    var sqlResult = await CreateSqlBackupInternal();

                    backupResult = new BackupResult
                    {
                        Success = jsonResult.Success && sqlResult.Success,
                        BackupPath = jsonResult.Success ? jsonResult.BackupPath : sqlResult.BackupPath,
                        Message = $"JSON: {jsonResult.Message}, SQL: {sqlResult.Message}"
                    };
                }

                var endTime = DateTime.UtcNow;
                var duration = endTime - startTime;

                // رفع للسحابة إذا كان مفعل
                if (backupResult.Success && schedule.UploadToCloud && schedule.CloudConfigId.HasValue && schedule.CloudConfig != null)
                {
                    var uploadRequest = new CloudUploadRequest
                    {
                        ConfigId = schedule.CloudConfigId.Value,
                        LocalFilePath = backupResult.BackupPath,
                        BackupType = "Scheduled",
                        Tags = $"scheduled,{schedule.ScheduleName.ToLower()}",
                        Notes = $"نسخة احتياطية مجدولة من {schedule.ScheduleName}",
                        DeleteLocalAfterUpload = schedule.DeleteLocalAfterUpload
                    };

                    var uploadResult = await _cloudStorageManager.UploadFileAsync(uploadRequest);

                    if (uploadResult.Success)
                    {
                        backupResult.Message += " - تم الرفع للسحابة بنجاح";
                    }
                    else
                    {
                        backupResult.Message += $" - فشل الرفع للسحابة: {uploadResult.Message}";
                    }
                }

                // تحديث الإحصائيات
                schedule.LastRunTime = endTime;
                schedule.LastRunDuration = duration;
                schedule.LastRunMessage = backupResult.Message;

                if (backupResult.Success)
                {
                    schedule.LastRunStatus = "Success";
                    schedule.SuccessfulRuns++;
                }
                else
                {
                    schedule.LastRunStatus = "Failed";
                    schedule.FailedRuns++;
                }

                // حساب متوسط مدة التشغيل
                if (schedule.AverageRunDuration.HasValue)
                {
                    schedule.AverageRunDuration = TimeSpan.FromTicks(
                        (schedule.AverageRunDuration.Value.Ticks + duration.Ticks) / 2);
                }
                else
                {
                    schedule.AverageRunDuration = duration;
                }

                await _context.SaveChangesAsync();

                return backupResult;
            }
            catch (Exception ex)
            {
                // تحديث حالة الفشل
                schedule.LastRunTime = DateTime.UtcNow;
                schedule.LastRunStatus = "Failed";
                schedule.LastRunMessage = $"خطأ في تشغيل النسخ الاحتياطي: {ex.Message}";
                schedule.FailedRuns++;
                await _context.SaveChangesAsync();

                return new BackupResult { Success = false, Message = ex.Message };
            }
        }

        private async Task<BackupResult> CreateJsonBackupInternal()
        {
            try
            {
                var backupFileName = $"AppDevDB_scheduled_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.json";
                var backupFilePath = Path.Combine(_backupFolderPath, backupFileName);

                var dataToBackup = new Dictionary<string, object>();

                var tableNames = _dbContext.Model.GetEntityTypes()
                    .Select(t => t.GetTableName())
                    .Where(t => t != null)
                    .Distinct()
                    .ToList();

                foreach (var tableName in tableNames)
                {
                    if (tableName != null)
                    {
                        var entityType = _dbContext.Model.GetEntityTypes().FirstOrDefault(e => e.GetTableName() == tableName);
                        if (entityType != null)
                        {
                            var data = await GetTableDataGeneric(entityType.ClrType);
                            dataToBackup[tableName] = data;
                        }
                    }
                }

                var json = JsonSerializer.Serialize(dataToBackup, new JsonSerializerOptions { WriteIndented = true });
                await System.IO.File.WriteAllTextAsync(backupFilePath, json);

                // ضغط الملف
                var zipFileName = $"AppDevDB_scheduled_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.zip";
                var zipFilePath = Path.Combine(_backupFolderPath, zipFileName);

                using (var archive = ZipFile.Open(zipFilePath, ZipArchiveMode.Create))
                {
                    var entry = archive.CreateEntry(Path.GetFileName(backupFileName), CompressionLevel.Optimal);
                    using (var entryStream = entry.Open())
                    using (var fileStream = System.IO.File.OpenRead(backupFilePath))
                    {
                        await fileStream.CopyToAsync(entryStream);
                    }
                }

                // حذف الملف المؤقت
                System.IO.File.Delete(backupFilePath);

                return new BackupResult { Success = true, BackupPath = zipFilePath, Message = "تم إنشاء النسخة الاحتياطية JSON بنجاح" };
            }
            catch (Exception ex)
            {
                return new BackupResult { Success = false, Message = $"خطأ في النسخ الاحتياطي JSON: {ex.Message}" };
            }
        }

        private async Task<BackupResult> CreateSqlBackupInternal()
        {
            try
            {
                var backupFileName = $"AppDevDB_scheduled_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.sql";
                var backupFilePath = Path.Combine(_backupFolderPath, backupFileName);

                var connectionString = _dbContext.Database.GetConnectionString();
                var connectionStringBuilder = new MySqlConnector.MySqlConnectionStringBuilder(connectionString);
                var databaseName = connectionStringBuilder.Database;

                var mysqldumpPath = "mysqldump"; // Assuming mysqldump is in PATH
                var arguments = $"--host={connectionStringBuilder.Server} --user={connectionStringBuilder.UserID} --password={connectionStringBuilder.Password} --single-transaction --routines --triggers {databaseName}";

                var processStartInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = mysqldumpPath,
                    Arguments = arguments,
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using (var process = System.Diagnostics.Process.Start(processStartInfo))
                {
                    if (process != null)
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        await process.WaitForExitAsync();

                        if (process.ExitCode == 0)
                        {
                            await System.IO.File.WriteAllTextAsync(backupFilePath, output);

                            // ضغط الملف
                            var zipFileName = $"AppDevDB_scheduled_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.zip";
                            var zipFilePath = Path.Combine(_backupFolderPath, zipFileName);

                            using (var archive = ZipFile.Open(zipFilePath, ZipArchiveMode.Create))
                            {
                                var entry = archive.CreateEntry(Path.GetFileName(backupFileName), CompressionLevel.Optimal);
                                using (var entryStream = entry.Open())
                                using (var fileStream = System.IO.File.OpenRead(backupFilePath))
                                {
                                    await fileStream.CopyToAsync(entryStream);
                                }
                            }

                            // حذف الملف المؤقت
                            System.IO.File.Delete(backupFilePath);

                            return new BackupResult { Success = true, BackupPath = zipFilePath, Message = "تم إنشاء النسخة الاحتياطية SQL بنجاح" };
                        }
                        else
                        {
                            return new BackupResult { Success = false, Message = "فشل في تشغيل mysqldump" };
                        }
                    }
                    else
                    {
                        return new BackupResult { Success = false, Message = "فشل في بدء عملية mysqldump" };
                    }
                }
            }
            catch (Exception ex)
            {
                return new BackupResult { Success = false, Message = $"خطأ في النسخ الاحتياطي SQL: {ex.Message}" };
            }
        }

        private async Task<List<object>> GetTableDataGeneric(Type entityType)
        {
            var method = typeof(BackupController).GetMethod("GetTableDataGenericHelper", BindingFlags.NonPublic | BindingFlags.Instance)?.MakeGenericMethod(entityType);
            if (method != null)
            {
                var task = (Task<List<object>>?)method.Invoke(this, null);
                if (task != null)
                    return await task;
            }
            return new List<object>();
        }

        private async Task<List<object>> GetTableDataGenericHelper<T>() where T : class
        {
            var data = await _dbContext.Set<T>().ToListAsync();
            return data.Cast<object>().ToList();
        }
    }

    // Dashboard Data Models
    public class BackupDashboardData
    {
        public int LocalBackupsCount { get; set; }
        public double LocalBackupsSizeGB { get; set; }
        public int CloudBackupsCount { get; set; }
        public double CloudBackupsSizeGB { get; set; }
        public int ActiveSchedulesCount { get; set; }
        public int TotalSchedulesCount { get; set; }
        public int CloudConfigsCount { get; set; }
        public int ActiveCloudConfigsCount { get; set; }
        public List<RecentBackupDto>? RecentBackups { get; set; }
        public List<ScheduleStatusDto>? ScheduleStatus { get; set; }
    }

    public class RecentBackupDto
    {
        public string FileName { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public double FileSizeGB { get; set; }
        public bool IsCloudBackup { get; set; }
        public string? BackupType { get; set; }
    }

    public class ScheduleStatusDto
    {
        public string ScheduleName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime? LastRunTime { get; set; }
        public string? LastRunStatus { get; set; }
        public DateTime? NextRunTime { get; set; }
    }

    public class InstantBackupRequest
    {
        public string BackupType { get; set; } = "JSON";
        public bool UploadToCloud { get; set; } = false;
        public int? CloudConfigId { get; set; }
        public bool DeleteLocalAfterUpload { get; set; } = false;
        public string? Notes { get; set; }
    }
}
