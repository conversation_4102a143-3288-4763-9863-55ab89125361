@page "/reports"
@page "/reports/index"
@inject NotificationService NotificationService
@inject NavigationManager Navigation

<PageTitle>إدارة التقارير</PageTitle>

<RadzenStack Gap="1rem">
    <RadzenRow>
        <RadzenColumn Size="12">
            <RadzenText TextStyle="TextStyle.H3" TagName="TagName.H1" class="rz-pt-8">
                <RadzenIcon Icon="assessment" class="rz-me-1" />
                إدارة التقارير - Stimulsoft
            </RadzenText>
        </RadzenColumn>
    </RadzenRow>

    <!-- Statistics Cards -->
    <RadzenRow Gap="1rem">
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="assessment" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">0</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">إجمالي التقارير</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
        
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4" Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="visibility" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">0</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">التقارير النشطة</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
        
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4" Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="design_services" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">2</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">المصمم والعارض</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
        
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4" Style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="library_books" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">Stimulsoft</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">مكتبة التقارير</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
    </RadzenRow>

    <!-- Main Actions -->
    <RadzenCard class="rz-p-4">
        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" Wrap="FlexWrap.Wrap" JustifyContent="JustifyContent.Center">
            <RadzenButton Text="إنشاء تقرير جديد" Icon="add_circle" ButtonStyle="ButtonStyle.Primary" Size="ButtonSize.Large"
                Click="@CreateNewReport" Style="min-width: 200px; height: 60px;" />
            
            <RadzenButton Text="عرض تقرير تجريبي" Icon="visibility" ButtonStyle="ButtonStyle.Success" Size="ButtonSize.Large"
                Click="@ViewSampleReport" Style="min-width: 200px; height: 60px;" />
        </RadzenStack>
    </RadzenCard>

    <!-- Features Overview -->
    <RadzenCard class="rz-p-4">
        <RadzenText TextStyle="TextStyle.H5" class="rz-mb-3">
            <RadzenIcon Icon="star" class="rz-me-1" />
            ميزات نظام التقارير
        </RadzenText>
        
        <RadzenRow Gap="1rem">
            <RadzenColumn Size="12" SizeMD="6">
                <RadzenCard class="rz-p-3" Style="border: 1px solid #e0e0e0;">
                    <RadzenStack Gap="0.5rem">
                        <RadzenText TextStyle="TextStyle.H6" class="rz-mb-0">
                            <RadzenIcon Icon="design_services" class="rz-me-1" Style="color: #2196f3;" />
                            مصمم التقارير
                        </RadzenText>
                        <RadzenText TextStyle="TextStyle.Body2">
                            مصمم تقارير متقدم باستخدام مكتبة Stimulsoft مع واجهة سهلة الاستخدام
                        </RadzenText>
                        <RadzenButton Text="فتح المصمم" Icon="launch" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.Small"
                            Click="@CreateNewReport" />
                    </RadzenStack>
                </RadzenCard>
            </RadzenColumn>
            
            <RadzenColumn Size="12" SizeMD="6">
                <RadzenCard class="rz-p-3" Style="border: 1px solid #e0e0e0;">
                    <RadzenStack Gap="0.5rem">
                        <RadzenText TextStyle="TextStyle.H6" class="rz-mb-0">
                            <RadzenIcon Icon="visibility" class="rz-me-1" Style="color: #4caf50;" />
                            عارض التقارير
                        </RadzenText>
                        <RadzenText TextStyle="TextStyle.Body2">
                            عارض تقارير تفاعلي مع إمكانيات الطباعة والتصدير لصيغ متعددة
                        </RadzenText>
                        <RadzenButton Text="عرض تجريبي" Icon="launch" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.Small"
                            Click="@ViewSampleReport" />
                    </RadzenStack>
                </RadzenCard>
            </RadzenColumn>
        </RadzenRow>
    </RadzenCard>

    <!-- Quick Links -->
    <RadzenCard class="rz-p-4">
        <RadzenText TextStyle="TextStyle.H5" class="rz-mb-3">
            <RadzenIcon Icon="link" class="rz-me-1" />
            روابط سريعة
        </RadzenText>
        
        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" Wrap="FlexWrap.Wrap">
            <RadzenButton Text="مصمم جديد" Icon="add" ButtonStyle="ButtonStyle.Light"
                Click="@CreateNewReport" />
            <RadzenButton Text="عارض تجريبي" Icon="preview" ButtonStyle="ButtonStyle.Light"
                Click="@ViewSampleReport" />
            <RadzenButton Text="تقرير رقم 1" Icon="description" ButtonStyle="ButtonStyle.Light"
                Click="@(() => ViewReport(1))" />
            <RadzenButton Text="تقرير رقم 2" Icon="description" ButtonStyle="ButtonStyle.Light"
                Click="@(() => ViewReport(2))" />
            <RadzenButton Text="تقرير رقم 3" Icon="description" ButtonStyle="ButtonStyle.Light"
                Click="@(() => ViewReport(3))" />
        </RadzenStack>
    </RadzenCard>
</RadzenStack>

@code {
    private async Task CreateNewReport()
    {
        Navigation.NavigateTo("/report-designer");
    }

    private async Task ViewSampleReport()
    {
        Navigation.NavigateTo("/report-viewer/0");
    }

    private async Task ViewReport(int reportId)
    {
        Navigation.NavigateTo($"/report-viewer/{reportId}");
    }
}
