﻿using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AppDev.Server.Migrations
{
    /// <inheritdoc />
    public partial class AddAppReportTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "App_ReportPrintSettings",
                columns: table => new
                {
                    PrintSettingId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    SettingName = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    PaperSize = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    Orientation = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    MarginTop = table.Column<decimal>(type: "decimal(65,30)", nullable: true),
                    MarginBottom = table.Column<decimal>(type: "decimal(65,30)", nullable: true),
                    MarginLeft = table.Column<decimal>(type: "decimal(65,30)", nullable: true),
                    MarginRight = table.Column<decimal>(type: "decimal(65,30)", nullable: true),
                    PrintHeader = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    PrintFooter = table.Column<bool>(type: "tinyint(1)", nullable: true),
                    HeaderText = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    FooterText = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    SettingCreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    SettingLastModified = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LastModifiedByUserId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_App_ReportPrintSettings", x => x.PrintSettingId);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "App_Reports",
                columns: table => new
                {
                    ReportId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    ReportName = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ReportDescription = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ReportDataSource = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ReportMetadata = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedByUserId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LastModifiedByUserId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    LastModifiedDate = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    IsActive = table.Column<bool>(type: "tinyint(1)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_App_Reports", x => x.ReportId);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "App_ReportDesigns",
                columns: table => new
                {
                    DesignId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    PrintSettingId = table.Column<int>(type: "int", nullable: true),
                    DesignName = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    ReportDefinition = table.Column<byte[]>(type: "longblob", nullable: true),
                    ReportPath = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DesignCreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    DesignLastModified = table.Column<DateTime>(type: "datetime(6)", nullable: true),
                    DesignVersion = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    DesignIsActive = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    ReportId = table.Column<int>(type: "int", nullable: true),
                    CreatedByUserId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    LastModifiedByUserId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_App_ReportDesigns", x => x.DesignId);
                    table.ForeignKey(
                        name: "FK_App_ReportDesigns_App_ReportPrintSettings_PrintSettingId",
                        column: x => x.PrintSettingId,
                        principalTable: "App_ReportPrintSettings",
                        principalColumn: "PrintSettingId");
                    table.ForeignKey(
                        name: "FK_App_ReportDesigns_App_Reports_ReportId",
                        column: x => x.ReportId,
                        principalTable: "App_Reports",
                        principalColumn: "ReportId");
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateTable(
                name: "App_ReportPermissions",
                columns: table => new
                {
                    PermissionId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                    ReportId = table.Column<int>(type: "int", nullable: false),
                    UserId = table.Column<string>(type: "longtext", nullable: false)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    RoleId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4"),
                    CanView = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CanEdit = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CanDelete = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CanPrint = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CanExport = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CanShare = table.Column<bool>(type: "tinyint(1)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "longtext", nullable: true)
                        .Annotation("MySql:CharSet", "utf8mb4")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_App_ReportPermissions", x => x.PermissionId);
                    table.ForeignKey(
                        name: "FK_App_ReportPermissions_App_Reports_ReportId",
                        column: x => x.ReportId,
                        principalTable: "App_Reports",
                        principalColumn: "ReportId",
                        onDelete: ReferentialAction.Cascade);
                })
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.CreateIndex(
                name: "IX_App_ReportDesigns_PrintSettingId",
                table: "App_ReportDesigns",
                column: "PrintSettingId");

            migrationBuilder.CreateIndex(
                name: "IX_App_ReportDesigns_ReportId",
                table: "App_ReportDesigns",
                column: "ReportId");

            migrationBuilder.CreateIndex(
                name: "IX_App_ReportPermissions_ReportId",
                table: "App_ReportPermissions",
                column: "ReportId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "App_ReportDesigns");

            migrationBuilder.DropTable(
                name: "App_ReportPermissions");

            migrationBuilder.DropTable(
                name: "App_ReportPrintSettings");

            migrationBuilder.DropTable(
                name: "App_Reports");
        }
    }
}
