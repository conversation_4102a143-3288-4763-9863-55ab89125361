@using AppDev.Server.Models.AppDevDB
@using System.Text.Json
@inject HttpClient Http
@inject NotificationService NotificationService
@inject DialogService DialogService

<RadzenStack Gap="1rem">
    <RadzenCard class="rz-p-4">
        <RadzenStack Gap="1rem">
            <RadzenText TextStyle="TextStyle.H6">تحديد الخطأ كمحلول</RadzenText>
            
            <RadzenRow>
                <RadzenColumn Size="12">
                    <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">معرف الخطأ:</RadzenText>
                    <RadzenText TextStyle="TextStyle.Body1">@ErrorLog?.ErrorId</RadzenText>
                </RadzenColumn>
            </RadzenRow>

            <RadzenRow>
                <RadzenColumn Size="12">
                    <RadzenText TextStyle="TextStyle.Subtitle2" class="rz-mb-2">رسالة الخطأ:</RadzenText>
                    <RadzenCard class="rz-p-3" Style="background-color: var(--rz-base-100);">
                        <RadzenText TextStyle="TextStyle.Body2">@ErrorLog?.Message</RadzenText>
                    </RadzenCard>
                </RadzenColumn>
            </RadzenRow>

            <RadzenRow>
                <RadzenColumn Size="12">
                    <RadzenLabel Text="ملاحظات الحل (اختياري)" />
                    <RadzenTextArea @bind-Value="resolutionNotes" Rows="4" 
                        Placeholder="اكتب ملاحظات حول كيفية حل هذا الخطأ..." 
                        class="w-100" />
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenCard>

    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" Gap="1rem">
        <RadzenButton Text="تحديد كمحلول" Icon="check" ButtonStyle="ButtonStyle.Success" 
            Click="@ResolveError" IsBusy="@isResolving" />
        <RadzenButton Text="إلغاء" Icon="close" ButtonStyle="ButtonStyle.Secondary" 
            Click="@(() => DialogService.Close(false))" Disabled="@isResolving" />
    </RadzenStack>
</RadzenStack>

@code {
    [Parameter] public AppErrorLog ErrorLog { get; set; }

    private string resolutionNotes = "";
    private bool isResolving = false;

    private async Task ResolveError()
    {
        isResolving = true;
        try
        {
            var request = new { ResolutionNotes = resolutionNotes };
            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

            var response = await Http.PostAsync($"api/ErrorLog/resolve/{ErrorLog.ErrorId}", content);

            if (response.IsSuccessStatusCode)
            {
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Success,
                    Summary = "نجح",
                    Detail = "تم تحديد الخطأ كمحلول بنجاح"
                });

                DialogService.Close(true);
            }
            else
            {
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Error,
                    Summary = "خطأ",
                    Detail = "فشل في تحديد الخطأ كمحلول"
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = $"حدث خطأ: {ex.Message}"
            });
        }
        finally
        {
            isResolving = false;
        }
    }
}
