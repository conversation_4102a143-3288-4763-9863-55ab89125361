using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using AppDev.Server.Data;
using AppDev.Server.Models.AppDevDB;
using System.Text.Json;

namespace AppDev.Server.Services
{
    public interface IErrorLoggingService
    {
        Task LogErrorAsync(string message, Exception exception = null, string source = null, string additionalData = null);
        Task LogWarningAsync(string message, string source = null, string additionalData = null);
        Task LogInformationAsync(string message, string source = null, string additionalData = null);
        Task<bool> MarkAsResolvedAsync(long errorId, string resolutionNotes = null);
        Task CleanupOldLogsAsync(int daysToKeep = 30);
    }

    public class ErrorLoggingService : IErrorLoggingService
    {
        private readonly AppDevDBContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<ErrorLoggingService> _logger;

        public ErrorLoggingService(
            AppDevDBContext context,
            IHttpContextAccessor httpContextAccessor,
            ILogger<ErrorLoggingService> logger)
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        public async Task LogErrorAsync(string message, Exception exception = null, string source = null, string additionalData = null)
        {
            await LogAsync("Error", message, exception, source, additionalData);
        }

        public async Task LogWarningAsync(string message, string source = null, string additionalData = null)
        {
            await LogAsync("Warning", message, null, source, additionalData);
        }

        public async Task LogInformationAsync(string message, string source = null, string additionalData = null)
        {
            await LogAsync("Information", message, null, source, additionalData);
        }

        private async Task LogAsync(string logLevel, string message, Exception exception = null, string source = null, string additionalData = null)
        {
            try
            {
                var httpContext = _httpContextAccessor.HttpContext;
                var userId = GetCurrentUserId();

                var errorLog = new AppErrorLog
                {
                    UserId = userId ?? "Anonymous",
                    LogLevel = logLevel,
                    Message = message?.Length > 500 ? message.Substring(0, 500) : message ?? "",
                    Exception = exception?.ToString(),
                    Source = source ?? GetCallerSource(),
                    RequestPath = httpContext?.Request?.Path.Value,
                    HttpMethod = httpContext?.Request?.Method,
                    IpAddress = GetClientIpAddress(),
                    UserAgent = httpContext?.Request?.Headers["User-Agent"].ToString(),
                    AdditionalData = additionalData,
                    CreatedDate = DateTime.UtcNow,
                    MachineName = Environment.MachineName,
                    ApplicationName = "AppDev"
                };

                _context.AppErrorLogs.Add(errorLog);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Fallback to system logger if database logging fails
                _logger.LogError(ex, "Failed to log error to database: {Message}", message);
            }
        }

        public async Task<bool> MarkAsResolvedAsync(long errorId, string resolutionNotes = null)
        {
            try
            {
                var errorLog = await _context.AppErrorLogs.FindAsync(errorId);
                if (errorLog != null)
                {
                    errorLog.IsResolved = true;
                    errorLog.ResolvedDate = DateTime.UtcNow;
                    errorLog.ResolvedByUserId = GetCurrentUserId();
                    errorLog.ResolutionNotes = resolutionNotes;

                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to mark error {ErrorId} as resolved", errorId);
                return false;
            }
        }

        public async Task CleanupOldLogsAsync(int daysToKeep = 30)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
                var oldLogs = await _context.AppErrorLogs
                    .Where(log => log.CreatedDate < cutoffDate)
                    .ToListAsync();

                if (oldLogs.Any())
                {
                    _context.AppErrorLogs.RemoveRange(oldLogs);
                    await _context.SaveChangesAsync();
                    
                    _logger.LogInformation("Cleaned up {Count} old error logs older than {Days} days", 
                        oldLogs.Count, daysToKeep);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup old error logs");
            }
        }

        private string GetCurrentUserId()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            return httpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        }

        private string GetClientIpAddress()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null) return null;

            var ipAddress = httpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = httpContext.Request.Headers["X-Real-IP"].FirstOrDefault();
            }
            if (string.IsNullOrEmpty(ipAddress))
            {
                ipAddress = httpContext.Connection.RemoteIpAddress?.ToString();
            }

            return ipAddress;
        }

        private string GetCallerSource()
        {
            try
            {
                var stackTrace = new System.Diagnostics.StackTrace(true);
                var frame = stackTrace.GetFrame(3); // Skip current method and LogAsync
                return frame?.GetMethod()?.DeclaringType?.Name;
            }
            catch
            {
                return "Unknown";
            }
        }
    }
}
