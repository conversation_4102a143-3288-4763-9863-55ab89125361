using AppDev.Server.Data;
using AppDev.Server.Models;
using AppDev.Server.Models.AppDevDB;
using Microsoft.EntityFrameworkCore;

namespace AppDev.Server.Services
{
    public interface ICloudStorageManager
    {
        Task<CloudStorageTestResult> TestConnectionAsync(int configId);
        Task<CloudUploadResult> UploadFileAsync(CloudUploadRequest request);
        Task<bool> DeleteFileAsync(int configId, string cloudFileId);
        Task<CloudStorageQuotaInfo> GetQuotaInfoAsync(int configId);
        Task<List<CloudFileInfo>> ListFilesAsync(int configId, string? folderPath = null);
        Task<Stream> DownloadFileAsync(int configId, string cloudFileId);
        Task<bool> CreateFolderAsync(int configId, string folderPath);
        Task<string> GetShareLinkAsync(int configId, string cloudFileId);
        Task<List<CloudStorageProviderDto>> GetProvidersAsync();
        Task<List<CloudStorageConfigDto>> GetConfigsAsync();
        Task<CloudStorageConfigDto?> GetConfigAsync(int configId);
        Task<CloudStorageConfigDto> CreateConfigAsync(CreateCloudStorageConfigRequest request, string userId);
        Task<CloudStorageConfigDto?> UpdateConfigAsync(UpdateCloudStorageConfigRequest request, string userId);
        Task<bool> DeleteConfigAsync(int configId);
        Task<bool> SetDefaultConfigAsync(int configId);
    }

    public class CloudStorageManager : ICloudStorageManager
    {
        private readonly AppDevDBContext _dbContext;
        private readonly GoogleDriveService _googleDriveService;
        private readonly MegaService _megaService;
        private readonly ILogger<CloudStorageManager> _logger;

        public CloudStorageManager(
            AppDevDBContext dbContext,
            GoogleDriveService googleDriveService,
            MegaService megaService,
            ILogger<CloudStorageManager> logger)
        {
            _dbContext = dbContext;
            _googleDriveService = googleDriveService;
            _megaService = megaService;
            _logger = logger;
        }

        public async Task<CloudStorageTestResult> TestConnectionAsync(int configId)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null)
            {
                return new CloudStorageTestResult
                {
                    Success = false,
                    Message = "إعدادات التخزين السحابي غير موجودة",
                    ErrorCode = "CONFIG_NOT_FOUND"
                };
            }

            var service = GetCloudStorageService(config.Provider.ProviderType);
            return await service.TestConnectionAsync(configId);
        }

        public async Task<CloudUploadResult> UploadFileAsync(CloudUploadRequest request)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == request.ConfigId);

            if (config == null)
            {
                return new CloudUploadResult
                {
                    Success = false,
                    Message = "إعدادات التخزين السحابي غير موجودة",
                    ErrorCode = "CONFIG_NOT_FOUND"
                };
            }

            var service = GetCloudStorageService(config.Provider.ProviderType);
            return await service.UploadFileAsync(request);
        }

        public async Task<bool> DeleteFileAsync(int configId, string cloudFileId)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null) return false;

            var service = GetCloudStorageService(config.Provider.ProviderType);
            return await service.DeleteFileAsync(configId, cloudFileId);
        }

        public async Task<CloudStorageQuotaInfo> GetQuotaInfoAsync(int configId)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null) return new CloudStorageQuotaInfo();

            var service = GetCloudStorageService(config.Provider.ProviderType);
            return await service.GetQuotaInfoAsync(configId);
        }

        public async Task<List<CloudFileInfo>> ListFilesAsync(int configId, string? folderPath = null)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null) return new List<CloudFileInfo>();

            var service = GetCloudStorageService(config.Provider.ProviderType);
            return await service.ListFilesAsync(configId, folderPath);
        }

        public async Task<Stream> DownloadFileAsync(int configId, string cloudFileId)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null) throw new InvalidOperationException("إعدادات التخزين السحابي غير موجودة");

            var service = GetCloudStorageService(config.Provider.ProviderType);
            return await service.DownloadFileAsync(configId, cloudFileId);
        }

        public async Task<bool> CreateFolderAsync(int configId, string folderPath)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null) return false;

            var service = GetCloudStorageService(config.Provider.ProviderType);
            return await service.CreateFolderAsync(configId, folderPath);
        }

        public async Task<string> GetShareLinkAsync(int configId, string cloudFileId)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null) return string.Empty;

            var service = GetCloudStorageService(config.Provider.ProviderType);
            return await service.GetShareLinkAsync(configId, cloudFileId);
        }

        public async Task<List<CloudStorageProviderDto>> GetProvidersAsync()
        {
            var providers = await _dbContext.AppCloudStorageProviders
                .Where(p => p.IsActive)
                .OrderBy(p => p.ProviderName)
                .ToListAsync();

            return providers.Select(p => new CloudStorageProviderDto
            {
                ProviderId = p.ProviderId,
                ProviderName = p.ProviderName,
                ProviderType = p.ProviderType,
                Description = p.Description,
                IsActive = p.IsActive,
                RequiresAuthentication = p.RequiresAuthentication,
                SupportedFileTypes = p.SupportedFileTypes,
                MaxFileSizeBytes = p.MaxFileSizeBytes,
                CreatedDate = p.CreatedDate
            }).ToList();
        }

        public async Task<List<CloudStorageConfigDto>> GetConfigsAsync()
        {
            var configs = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .Where(c => c.IsActive)
                .OrderBy(c => c.ConfigName)
                .ToListAsync();

            return configs.Select(c => new CloudStorageConfigDto
            {
                ConfigId = c.ConfigId,
                ProviderId = c.ProviderId,
                ConfigName = c.ConfigName,
                Description = c.Description,
                FolderPath = c.FolderPath,
                FileNamePrefix = c.FileNamePrefix,
                AutoUpload = c.AutoUpload,
                DeleteLocalAfterUpload = c.DeleteLocalAfterUpload,
                RetentionDays = c.RetentionDays,
                IsActive = c.IsActive,
                IsDefault = c.IsDefault,
                LastSyncDate = c.LastSyncDate,
                LastSyncStatus = c.LastSyncStatus,
                ProviderName = c.Provider.ProviderName,
                ProviderType = c.Provider.ProviderType
            }).ToList();
        }

        public async Task<CloudStorageConfigDto?> GetConfigAsync(int configId)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null) return null;

            return new CloudStorageConfigDto
            {
                ConfigId = config.ConfigId,
                ProviderId = config.ProviderId,
                ConfigName = config.ConfigName,
                Description = config.Description,
                FolderPath = config.FolderPath,
                FileNamePrefix = config.FileNamePrefix,
                AutoUpload = config.AutoUpload,
                DeleteLocalAfterUpload = config.DeleteLocalAfterUpload,
                RetentionDays = config.RetentionDays,
                IsActive = config.IsActive,
                IsDefault = config.IsDefault,
                LastSyncDate = config.LastSyncDate,
                LastSyncStatus = config.LastSyncStatus,
                ProviderName = config.Provider.ProviderName,
                ProviderType = config.Provider.ProviderType
            };
        }

        public async Task<CloudStorageConfigDto> CreateConfigAsync(CreateCloudStorageConfigRequest request, string userId)
        {
            var config = new AppCloudStorageConfig
            {
                ProviderId = request.ProviderId,
                ConfigName = request.ConfigName,
                Description = request.Description,
                ClientId = request.ClientId,
                ClientSecret = EncryptSensitiveData(request.ClientSecret),
                ApiKey = EncryptSensitiveData(request.ApiKey),
                Username = request.Username,
                EncryptedPassword = EncryptSensitiveData(request.Password),
                FolderPath = request.FolderPath,
                FileNamePrefix = request.FileNamePrefix,
                AutoUpload = request.AutoUpload,
                DeleteLocalAfterUpload = request.DeleteLocalAfterUpload,
                RetentionDays = request.RetentionDays,
                NotifyOnSuccess = request.NotifyOnSuccess,
                NotifyOnFailure = request.NotifyOnFailure,
                NotificationEmail = request.NotificationEmail,
                IsActive = true,
                IsDefault = request.IsDefault,
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = userId
            };

            // If this is set as default, unset other defaults
            if (request.IsDefault)
            {
                var existingDefaults = await _dbContext.AppCloudStorageConfigs
                    .Where(c => c.ProviderId == request.ProviderId && c.IsDefault)
                    .ToListAsync();

                foreach (var existing in existingDefaults)
                {
                    existing.IsDefault = false;
                }
            }

            _dbContext.AppCloudStorageConfigs.Add(config);
            await _dbContext.SaveChangesAsync();

            return await GetConfigAsync(config.ConfigId) ?? throw new InvalidOperationException("فشل في إنشاء الإعدادات");
        }

        public async Task<CloudStorageConfigDto?> UpdateConfigAsync(UpdateCloudStorageConfigRequest request, string userId)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .FirstOrDefaultAsync(c => c.ConfigId == request.ConfigId);

            if (config == null) return null;

            config.ConfigName = request.ConfigName;
            config.Description = request.Description;
            config.FolderPath = request.FolderPath;
            config.FileNamePrefix = request.FileNamePrefix;
            config.AutoUpload = request.AutoUpload;
            config.DeleteLocalAfterUpload = request.DeleteLocalAfterUpload;
            config.RetentionDays = request.RetentionDays;
            config.NotifyOnSuccess = request.NotifyOnSuccess;
            config.NotifyOnFailure = request.NotifyOnFailure;
            config.NotificationEmail = request.NotificationEmail;
            config.IsDefault = request.IsDefault;
            config.LastModifiedDate = DateTime.UtcNow;
            config.LastModifiedByUserId = userId;

            // Update sensitive data only if provided
            if (!string.IsNullOrEmpty(request.ClientId))
                config.ClientId = request.ClientId;
            if (!string.IsNullOrEmpty(request.ClientSecret))
                config.ClientSecret = EncryptSensitiveData(request.ClientSecret);
            if (!string.IsNullOrEmpty(request.ApiKey))
                config.ApiKey = EncryptSensitiveData(request.ApiKey);
            if (!string.IsNullOrEmpty(request.Username))
                config.Username = request.Username;
            if (!string.IsNullOrEmpty(request.Password))
                config.EncryptedPassword = EncryptSensitiveData(request.Password);

            // If this is set as default, unset other defaults
            if (request.IsDefault)
            {
                var existingDefaults = await _dbContext.AppCloudStorageConfigs
                    .Where(c => c.ProviderId == config.ProviderId && c.ConfigId != config.ConfigId && c.IsDefault)
                    .ToListAsync();

                foreach (var existing in existingDefaults)
                {
                    existing.IsDefault = false;
                }
            }

            await _dbContext.SaveChangesAsync();
            return await GetConfigAsync(config.ConfigId);
        }

        public async Task<bool> DeleteConfigAsync(int configId)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null) return false;

            config.IsActive = false;
            config.LastModifiedDate = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();
            return true;
        }

        public async Task<bool> SetDefaultConfigAsync(int configId)
        {
            var config = await _dbContext.AppCloudStorageConfigs
                .FirstOrDefaultAsync(c => c.ConfigId == configId);

            if (config == null) return false;

            // Unset other defaults for the same provider
            var existingDefaults = await _dbContext.AppCloudStorageConfigs
                .Where(c => c.ProviderId == config.ProviderId && c.ConfigId != configId && c.IsDefault)
                .ToListAsync();

            foreach (var existing in existingDefaults)
            {
                existing.IsDefault = false;
            }

            config.IsDefault = true;
            config.LastModifiedDate = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();
            return true;
        }

        private ICloudStorageService GetCloudStorageService(string providerType)
        {
            return providerType.ToLower() switch
            {
                "googledrive" => _googleDriveService,
                "mega" => _megaService,
                _ => throw new NotSupportedException($"مقدم الخدمة السحابية غير مدعوم: {providerType}")
            };
        }

        private string? EncryptSensitiveData(string? data)
        {
            if (string.IsNullOrEmpty(data)) return data;

            // TODO: Implement proper encryption
            // For now, use base64 encoding as a placeholder
            var bytes = System.Text.Encoding.UTF8.GetBytes(data);
            return Convert.ToBase64String(bytes);
        }
    }
}
