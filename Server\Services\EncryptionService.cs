using System.Security.Cryptography;
using System.Text;

namespace AppDev.Server.Services
{
    public class EncryptionService
    {
        private const string DefaultSalt = "AppDev2024BackupSalt";
        private const int KeySize = 256;
        private const int IvSize = 128;
        private const int Iterations = 10000;

        public static byte[] EncryptData(byte[] data, string password)
        {
            try
            {
                using var aes = Aes.Create();
                aes.KeySize = KeySize;
                aes.BlockSize = IvSize;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                // إنشاء مفتاح من كلمة المرور
                var key = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes(DefaultSalt), Iterations, HashAlgorithmName.SHA256);
                aes.Key = key.GetBytes(KeySize / 8);
                aes.IV = key.GetBytes(IvSize / 8);

                using var encryptor = aes.CreateEncryptor();
                return encryptor.TransformFinalBlock(data, 0, data.Length);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في تشفير البيانات: {ex.Message}", ex);
            }
        }

        public static byte[] DecryptData(byte[] encryptedData, string password)
        {
            try
            {
                using var aes = Aes.Create();
                aes.KeySize = KeySize;
                aes.BlockSize = IvSize;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;

                // إنشاء مفتاح من كلمة المرور
                var key = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes(DefaultSalt), Iterations, HashAlgorithmName.SHA256);
                aes.Key = key.GetBytes(KeySize / 8);
                aes.IV = key.GetBytes(IvSize / 8);

                using var decryptor = aes.CreateDecryptor();
                return decryptor.TransformFinalBlock(encryptedData, 0, encryptedData.Length);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في فك تشفير البيانات: {ex.Message}", ex);
            }
        }

        public static async Task<string> EncryptFile(string inputFilePath, string outputFilePath, string password)
        {
            try
            {
                var data = await File.ReadAllBytesAsync(inputFilePath);
                var encryptedData = EncryptData(data, password);
                await File.WriteAllBytesAsync(outputFilePath, encryptedData);
                return outputFilePath;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في تشفير الملف: {ex.Message}", ex);
            }
        }

        public static async Task<string> DecryptFile(string inputFilePath, string outputFilePath, string password)
        {
            try
            {
                var encryptedData = await File.ReadAllBytesAsync(inputFilePath);
                var decryptedData = DecryptData(encryptedData, password);
                await File.WriteAllBytesAsync(outputFilePath, decryptedData);
                return outputFilePath;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في فك تشفير الملف: {ex.Message}", ex);
            }
        }

        public static string GenerateSecurePassword(int length = 16)
        {
            const string validChars = "ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
            var random = new Random();
            var chars = new char[length];
            
            for (int i = 0; i < length; i++)
            {
                chars[i] = validChars[random.Next(validChars.Length)];
            }
            
            return new string(chars);
        }

        public static string ComputeFileHash(string filePath)
        {
            try
            {
                using var sha256 = SHA256.Create();
                using var stream = File.OpenRead(filePath);
                var hash = sha256.ComputeHash(stream);
                return Convert.ToBase64String(hash);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"خطأ في حساب hash الملف: {ex.Message}", ex);
            }
        }
    }
}
