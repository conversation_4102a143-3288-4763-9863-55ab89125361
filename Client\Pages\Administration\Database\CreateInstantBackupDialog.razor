#nullable enable
@using AppDev.Server.Models
@using System.Text.Json
@inject HttpClient Http
@inject NotificationService NotificationService
@inject DialogService DialogService



<RadzenStack Gap="1rem">
    <!-- Backup Type Selection -->
    <RadzenFieldset Text="نوع النسخة الاحتياطية">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12">
                    <RadzenLabel Text="اختر نوع النسخة الاحتياطية" />
                    <RadzenDropDown @bind-Value="@backupType" Data="@backupTypes" class="w-100" />
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenFieldset>

    <!-- Cloud Upload Options -->
    <RadzenFieldset Text="خيارات الرفع السحابي">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenStack Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="@uploadToCloud" Name="uploadToCloud" />
                        <RadzenLabel Text="رفع للتخزين السحابي" Component="uploadToCloud" />
                    </RadzenStack>
                </RadzenColumn>
                @if (uploadToCloud)
                {
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenLabel Text="إعدادات التخزين السحابي" />
                        <RadzenDropDown @bind-Value="@selectedCloudConfigId" Data="@cloudConfigs" TextProperty="ConfigName"
                            ValueProperty="ConfigId" Placeholder="اختر إعدادات التخزين السحابي" class="w-100" />
                    </RadzenColumn>
                }
            </RadzenRow>

            @if (uploadToCloud)
            {
                <RadzenRow Gap="1rem">
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenStack Gap="0.5rem">
                            <RadzenCheckBox @bind-Value="@deleteLocalAfterUpload" Name="deleteLocal" />
                            <RadzenLabel Text="حذف الملف المحلي بعد الرفع" Component="deleteLocal" />
                        </RadzenStack>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenLabel Text="ملاحظات (اختياري)" />
                        <RadzenTextBox @bind-Value="@notes" Placeholder="ملاحظات حول النسخة الاحتياطية" class="w-100" />
                    </RadzenColumn>
                </RadzenRow>
            }
        </RadzenStack>
    </RadzenFieldset>

    <!-- Action Buttons -->
    <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" JustifyContent="JustifyContent.End" class="rz-mt-4">
        <RadzenButton Text="إلغاء" Click="@Cancel" ButtonStyle="ButtonStyle.Light" />
        <RadzenButton Text="إنشاء النسخة الاحتياطية" Click="@CreateBackup" ButtonStyle="ButtonStyle.Primary"
            IsBusy="@isCreating" />
    </RadzenStack>
</RadzenStack>

@code {
    private string backupType = "JSON";
    private bool uploadToCloud = false;
    private int? selectedCloudConfigId;
    private bool deleteLocalAfterUpload = false;
    private string notes = string.Empty;
    private List<CloudStorageConfigDto> cloudConfigs = new();
    private bool isCreating = false;

    private readonly List<string> backupTypes = new() { "JSON", "SQL", "Both" };

    protected override async Task OnInitializedAsync()
    {
        await LoadCloudConfigs();
    }

    private async Task LoadCloudConfigs()
    {
        try
        {
            var response = await Http.GetAsync("api/backup/cloud/configs");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                cloudConfigs = JsonSerializer.Deserialize<List<CloudStorageConfigDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = $"فشل في تحميل إعدادات التخزين السحابي: {ex.Message}",
                Duration = 4000
            });
        }
    }

    private async Task CreateBackup()
    {
        if (uploadToCloud && !selectedCloudConfigId.HasValue)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = "تحذير",
                Detail = "يرجى اختيار إعدادات التخزين السحابي",
                Duration = 4000
            });
            return;
        }

        isCreating = true;
        StateHasChanged();

        try
        {
            // Create backup request
            var request = new
            {
                BackupType = backupType,
                UploadToCloud = uploadToCloud,
                CloudConfigId = selectedCloudConfigId,
                DeleteLocalAfterUpload = deleteLocalAfterUpload,
                Notes = notes
            };

            var response = await Http.PostAsJsonAsync("api/backup/instant", request);
            if (response.IsSuccessStatusCode)
            {
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Success,
                    Summary = "نجح",
                    Detail = "تم إنشاء النسخة الاحتياطية بنجاح",
                    Duration = 4000
                });

                DialogService.Close(true);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent);

                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Error,
                    Summary = "خطأ",
                    Detail = errorResult.GetProperty("Message").GetString() ?? "فشل في إنشاء النسخة الاحتياطية",
                    Duration = 4000
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = $"حدث خطأ: {ex.Message}",
                Duration = 4000
            });
        }
        finally
        {
            isCreating = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        DialogService.Close(false);
    }

    // Data models
    public class CloudStorageConfigDto
    {
        public int ConfigId { get; set; }
        public string ConfigName { get; set; } = string.Empty;
        public string ProviderName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }
}
