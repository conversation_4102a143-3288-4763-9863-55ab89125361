import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mobile_app/app/routes/app_pages.dart';

class AuthService extends GetxService {
  late SharedPreferences _prefs;
  final String _tokenKey = 'auth_token';
  final String _refreshTokenKey = 'refresh_token';

  Future<AuthService> init() async {
    _prefs = await SharedPreferences.getInstance();
    return this;
  }

  Future<void> saveTokens(String token, String refreshToken) async {
    await _prefs.setString(_tokenKey, token);
    await _prefs.setString(_refreshTokenKey, refreshToken);
  }

  String? getToken() {
    return _prefs.getString(_tokenKey);
  }

  String? getRefreshToken() {
    return _prefs.getString(_refreshTokenKey);
  }

  Future<void> logout() async {
    await _prefs.remove(_tokenKey);
    await _prefs.remove(_refreshTokenKey);
  }

  bool isAuthenticated() {
    return getToken() != null;
  }

  // This method will be called before the app starts to check authentication status
  String get initialRoute {
    return isAuthenticated() ? Routes.home : Routes.login; // Corrected to lowerCamelCase
  }
}