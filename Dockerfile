# --------- Build Stage ---------
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src


RUN apt-get update && \
    apt-get install -y --no-install-recommends libgdiplus && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*
# انسخ ملفات المشروع - باستخدام الأسماء الصحيحة
COPY AppDev.sln ./
COPY Server/AppDev.Server.csproj ./Server/
COPY Server/appsettings*.json ./Server/
COPY Client/AppDev.Client.csproj ./Client/



RUN dotnet restore

# انسخ كل الملفات الآن
COPY . .
WORKDIR /src/Server
RUN dotnet publish -c Release -o /app/publish

# --------- Runtime Stage ---------
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app
COPY --from=build /app/publish .
# Create a non-root user and set permissions
RUN useradd -m appuser
# The ENTRYPOINT will run as root to fix permissions, then switch to appuser
ENTRYPOINT ["sh", "-c", "chown -R appuser:appuser /app && exec su appuser -c 'dotnet AppDev.Server.dll'"]
