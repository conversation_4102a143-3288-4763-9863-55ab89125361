<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">
  <PropertyGroup>
    <NoWarn>CS0168,CS1998,BL9993,CS0649,CS0436,0436,<PERSON>8632,<PERSON>8629,CS8600,CS8601,CS8602,<PERSON>8603,CS8604,CS8618,CS8625,CS8981,CS8714,CS8669</NoWarn>
    <NuGetAuditMode>direct</NuGetAuditMode>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <StaticWebAssetProjectMode>Default</StaticWebAssetProjectMode>
    <ServiceWorkerAssetsManifest>service-worker-assets.js</ServiceWorkerAssetsManifest>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Authentication" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
    <PackageReference Include="Radzen.Blazor" Version="*" />

    <!-- Stimulsoft Reports for Blazor -->
    <PackageReference Include="Stimulsoft.Reports.Blazor" Version="2025.3.3" />

    <Compile Include="../Server/Models/**/*.cs" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ServiceWorker Include="../Server/wwwroot/service-worker.js" PublishedContent="../Server/wwwroot/service-worker.published.js" />
  </ItemGroup>
  <PropertyGroup>
    <BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
  </PropertyGroup>
</Project>