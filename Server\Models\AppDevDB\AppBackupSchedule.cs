using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AppDev.Server.Models.AppDevDB
{
    [Table("App_BackupSchedules")]
    public class AppBackupSchedule
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ScheduleId { get; set; }

        [Required]
        [StringLength(100)]
        public string ScheduleName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [StringLength(50)]
        public string BackupType { get; set; } = "JSON"; // JSON, SQL, Both

        [Required]
        [StringLength(50)]
        public string ScheduleType { get; set; } = "Daily"; // Daily, Weekly, Monthly, Custom

        [Required]
        [StringLength(100)]
        public string CronExpression { get; set; } = "0 2 * * *"; // Default: 2 AM daily

        public TimeSpan? ScheduleTime { get; set; }

        [StringLength(50)]
        public string? DayOfWeek { get; set; } // For weekly schedules

        public int? DayOfMonth { get; set; } // For monthly schedules

        public int IntervalHours { get; set; } = 24; // For custom intervals

        // Cloud Upload Settings
        public bool UploadToCloud { get; set; } = false;

        public int? CloudConfigId { get; set; }

        public bool DeleteLocalAfterUpload { get; set; } = false;

        // Retention Settings
        public int LocalRetentionDays { get; set; } = 7;

        public int CloudRetentionDays { get; set; } = 30;

        public int MaxLocalBackups { get; set; } = 10;

        public int MaxCloudBackups { get; set; } = 50;

        // Notification Settings
        public bool NotifyOnSuccess { get; set; } = false;

        public bool NotifyOnFailure { get; set; } = true;

        [StringLength(500)]
        public string? NotificationEmail { get; set; }

        [StringLength(1000)]
        public string? NotificationWebhook { get; set; }

        // Status and Execution
        [Required]
        public bool IsActive { get; set; } = true;

        public DateTime? LastRunTime { get; set; }

        public DateTime? NextRunTime { get; set; }

        [StringLength(50)]
        public string? LastRunStatus { get; set; } // Success, Failed, Partial

        [StringLength(1000)]
        public string? LastRunMessage { get; set; }

        public int SuccessfulRuns { get; set; } = 0;

        public int FailedRuns { get; set; } = 0;

        public TimeSpan? AverageRunDuration { get; set; }

        public TimeSpan? LastRunDuration { get; set; }

        // Advanced Settings
        public bool CompressBackups { get; set; } = true;

        public bool EncryptBackups { get; set; } = false;

        [StringLength(100)]
        public string? EncryptionKey { get; set; }

        public bool VerifyIntegrity { get; set; } = true;

        [StringLength(1000)]
        public string? CustomSettings { get; set; } // JSON for additional settings

        // Audit
        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime? LastModifiedDate { get; set; }

        [StringLength(450)]
        public string? CreatedByUserId { get; set; }

        [StringLength(450)]
        public string? LastModifiedByUserId { get; set; }

        // Navigation Properties
        [ForeignKey("CloudConfigId")]
        public virtual AppCloudStorageConfig? CloudConfig { get; set; }
    }
}
