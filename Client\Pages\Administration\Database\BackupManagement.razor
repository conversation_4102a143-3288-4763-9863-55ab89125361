@page "/admin/database/backup-management"
@using AppDev.Server.Models
@using System.Text.Json

<PageTitle>إدارة النسخ الاحتياطي الشاملة</PageTitle>

<RadzenStack Gap="1.5rem">
    <!-- Header -->
    <RadzenCard class="rz-p-4">
        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center">
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                <RadzenIcon Icon="backup" Style="font-size: 2rem; color: var(--rz-primary);" />
                <RadzenStack Gap="0.25rem">
                    <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">إدارة النسخ الاحتياطي الشاملة</RadzenText>
                    <RadzenText TextStyle="TextStyle.Body2" class="rz-color-secondary">
                        لوحة التحكم الموحدة لجميع عمليات النسخ الاحتياطي والتخزين السحابي
                    </RadzenText>
                </RadzenStack>
            </RadzenStack>
            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem">
                <RadzenButton Text="نسخة احتياطية فورية" Icon="flash_on" ButtonStyle="ButtonStyle.Primary" 
                             Click="@ShowInstantBackupDialog" Size="ButtonSize.Medium" />
                <RadzenButton Text="تحديث البيانات" Icon="refresh" ButtonStyle="ButtonStyle.Light" 
                             Click="@RefreshAllData" Size="ButtonSize.Medium" IsBusy="@isRefreshing" />
            </RadzenStack>
        </RadzenStack>
    </RadzenCard>

    <!-- Statistics Cards -->
    <RadzenRow Gap="1rem">
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-3 rz-text-align-center" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <RadzenStack Gap="0.5rem" AlignItems="AlignItems.Center">
                    <RadzenIcon Icon="folder" Style="font-size: 2rem;" />
                    <RadzenText TextStyle="TextStyle.H5" class="rz-mb-0">@dashboardData?.LocalBackupsCount</RadzenText>
                    <RadzenText TextStyle="TextStyle.Caption">النسخ المحلية</RadzenText>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-3 rz-text-align-center" Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                <RadzenStack Gap="0.5rem" AlignItems="AlignItems.Center">
                    <RadzenIcon Icon="cloud" Style="font-size: 2rem;" />
                    <RadzenText TextStyle="TextStyle.H5" class="rz-mb-0">@dashboardData?.CloudBackupsCount</RadzenText>
                    <RadzenText TextStyle="TextStyle.Caption">النسخ السحابية</RadzenText>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-3 rz-text-align-center" Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                <RadzenStack Gap="0.5rem" AlignItems="AlignItems.Center">
                    <RadzenIcon Icon="schedule" Style="font-size: 2rem;" />
                    <RadzenText TextStyle="TextStyle.H5" class="rz-mb-0">@dashboardData?.ActiveSchedulesCount</RadzenText>
                    <RadzenText TextStyle="TextStyle.Caption">الجداول النشطة</RadzenText>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-3 rz-text-align-center" Style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                <RadzenStack Gap="0.5rem" AlignItems="AlignItems.Center">
                    <RadzenIcon Icon="cloud_queue" Style="font-size: 2rem;" />
                    <RadzenText TextStyle="TextStyle.H5" class="rz-mb-0">@dashboardData?.ActiveCloudConfigsCount</RadzenText>
                    <RadzenText TextStyle="TextStyle.Caption">الحسابات السحابية</RadzenText>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
    </RadzenRow>

    <!-- Main Content Tabs -->
    <RadzenTabs @bind-SelectedIndex="selectedTabIndex" TabPosition="TabPosition.Top">
        <!-- Dashboard Tab -->
        <Tabs>
            <RadzenTabsItem Text="لوحة التحكم" Icon="dashboard">
                <RadzenStack Gap="1rem" class="rz-mt-4">
                    <!-- Quick Actions -->
                    <RadzenCard class="rz-p-4">
                        <RadzenText TextStyle="TextStyle.H6" class="rz-mb-3">الإجراءات السريعة</RadzenText>
                        <RadzenRow Gap="1rem">
                            <RadzenColumn Size="12" SizeMD="6" SizeLG="3">
                                <RadzenButton Text="نسخة احتياطية JSON" Icon="description" ButtonStyle="ButtonStyle.Success" 
                                             Click="@(() => CreateBackup("JSON"))" class="w-100" Size="ButtonSize.Large" />
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeMD="6" SizeLG="3">
                                <RadzenButton Text="نسخة احتياطية SQL" Icon="storage" ButtonStyle="ButtonStyle.Info" 
                                             Click="@(() => CreateBackup("SQL"))" class="w-100" Size="ButtonSize.Large" />
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeMD="6" SizeLG="3">
                                <RadzenButton Text="رفع للسحابة" Icon="cloud_upload" ButtonStyle="ButtonStyle.Warning" 
                                             Click="@ShowCloudUploadDialog" class="w-100" Size="ButtonSize.Large" />
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeMD="6" SizeLG="3">
                                <RadzenButton Text="جدول جديد" Icon="add_alarm" ButtonStyle="ButtonStyle.Secondary" 
                                             Click="@ShowCreateScheduleDialog" class="w-100" Size="ButtonSize.Large" />
                            </RadzenColumn>
                        </RadzenRow>
                    </RadzenCard>

                    <!-- Recent Activity -->
                    <RadzenRow Gap="1rem">
                        <RadzenColumn Size="12" SizeLG="8">
                            <RadzenCard class="rz-p-4">
                                <RadzenText TextStyle="TextStyle.H6" class="rz-mb-3">النسخ الاحتياطية الحديثة</RadzenText>
                                @if (dashboardData?.RecentBackups?.Any() == true)
                                {
                                    <RadzenDataGrid Data="@dashboardData.RecentBackups" TItem="RecentBackupDto" 
                                                   AllowPaging="true" PageSize="5" class="rz-mt-2">
                                        <Columns>
                                            <RadzenDataGridColumn TItem="RecentBackupDto" Property="FileName" Title="اسم الملف" />
                                            <RadzenDataGridColumn TItem="RecentBackupDto" Property="BackupType" Title="النوع" />
                                            <RadzenDataGridColumn TItem="RecentBackupDto" Property="CreatedDate" Title="التاريخ" 
                                                                 FormatString="{0:yyyy-MM-dd HH:mm}" />
                                            <RadzenDataGridColumn TItem="RecentBackupDto" Property="FileSizeGB" Title="الحجم (GB)" 
                                                                 FormatString="{0:F2}" />
                                        </Columns>
                                    </RadzenDataGrid>
                                }
                                else
                                {
                                    <RadzenAlert AlertStyle="AlertStyle.Info" Text="لا توجد نسخ احتياطية حديثة" />
                                }
                            </RadzenCard>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeLG="4">
                            <RadzenCard class="rz-p-4">
                                <RadzenText TextStyle="TextStyle.H6" class="rz-mb-3">حالة الجداول</RadzenText>
                                @if (dashboardData?.ScheduleStatus?.Any() == true)
                                {
                                    <RadzenStack Gap="0.5rem">
                                        @foreach (var schedule in dashboardData.ScheduleStatus.Take(5))
                                        {
                                            <RadzenCard class="rz-p-2" Style="@($"border-left: 4px solid {(schedule.IsActive ? "var(--rz-success)" : "var(--rz-danger)")}")">
                                                <RadzenStack Gap="0.25rem">
                                                    <RadzenText TextStyle="TextStyle.Body2" class="rz-mb-0">@schedule.ScheduleName</RadzenText>
                                                    <RadzenText TextStyle="TextStyle.Caption" class="rz-color-secondary">
                                                        @(schedule.IsActive ? "نشط" : "غير نشط") - 
                                                        آخر تشغيل: @(schedule.LastRunTime?.ToString("MM-dd HH:mm") ?? "لم يتم")
                                                    </RadzenText>
                                                </RadzenStack>
                                            </RadzenCard>
                                        }
                                    </RadzenStack>
                                }
                                else
                                {
                                    <RadzenAlert AlertStyle="AlertStyle.Info" Text="لا توجد جداول مكونة" />
                                }
                            </RadzenCard>
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenStack>
            </RadzenTabsItem>

            <!-- Backup Management Tab -->
            <RadzenTabsItem Text="إدارة النسخ" Icon="backup">
                <RadzenStack Gap="1rem" class="rz-mt-4">
                    <!-- Backup Actions -->
                    <RadzenCard class="rz-p-4">
                        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center">
                            <RadzenText TextStyle="TextStyle.H6">ملفات النسخ الاحتياطي</RadzenText>
                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem">
                                <RadzenButton Text="نسخة جديدة" Icon="add" ButtonStyle="ButtonStyle.Primary" 
                                             Click="@ShowInstantBackupDialog" Size="ButtonSize.Medium" />
                                <RadzenButton Text="تحديث" Icon="refresh" ButtonStyle="ButtonStyle.Light" 
                                             Click="@LoadBackupFiles" Size="ButtonSize.Medium" />
                            </RadzenStack>
                        </RadzenStack>
                    </RadzenCard>

                    <!-- Backup Files Grid -->
                    <RadzenCard class="rz-p-4">
                        @if (backupFiles?.Any() == true)
                        {
                            <RadzenDataGrid Data="@backupFiles" TItem="BackupInfo" AllowPaging="true" PageSize="10">
                                <Columns>
                                    <RadzenDataGridColumn TItem="BackupInfo" Property="FileName" Title="اسم الملف" />
                                    <RadzenDataGridColumn TItem="BackupInfo" Property="Type" Title="النوع" />
                                    <RadzenDataGridColumn TItem="BackupInfo" Property="CreatedAt" Title="تاريخ الإنشاء" 
                                                         FormatString="{0:yyyy-MM-dd HH:mm:ss}" />
                                    <RadzenDataGridColumn TItem="BackupInfo" Property="SizeBytes" Title="الحجم" 
                                                         FormatString="{0:N0} بايت" />
                                    <RadzenDataGridColumn TItem="BackupInfo" Title="الإجراءات" Sortable="false" Filterable="false">
                                        <Template Context="backup">
                                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem">
                                                <RadzenButton Icon="download" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => DownloadBackup(backup.FileName))" />
                                                <RadzenButton Icon="cloud_upload" ButtonStyle="ButtonStyle.Info" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => UploadToCloud(backup.FileName))" />
                                                <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => DeleteBackup(backup.FileName))" />
                                            </RadzenStack>
                                        </Template>
                                    </RadzenDataGridColumn>
                                </Columns>
                            </RadzenDataGrid>
                        }
                        else
                        {
                            <RadzenAlert AlertStyle="AlertStyle.Info" Text="لا توجد ملفات نسخ احتياطية" />
                        }
                    </RadzenCard>
                </RadzenStack>
            </RadzenTabsItem>

            <!-- Schedule Management Tab -->
            <RadzenTabsItem Text="جدولة النسخ" Icon="schedule">
                <RadzenStack Gap="1rem" class="rz-mt-4">
                    <!-- Schedule Actions -->
                    <RadzenCard class="rz-p-4">
                        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center">
                            <RadzenText TextStyle="TextStyle.H6">جداول النسخ الاحتياطي</RadzenText>
                            <RadzenButton Text="جدول جديد" Icon="add" ButtonStyle="ButtonStyle.Primary"
                                         Click="@ShowCreateScheduleDialog" Size="ButtonSize.Medium" />
                        </RadzenStack>
                    </RadzenCard>

                    <!-- Schedules Grid -->
                    <RadzenCard class="rz-p-4">
                        @if (schedules?.Any() == true)
                        {
                            <RadzenDataGrid Data="@schedules" TItem="dynamic" AllowPaging="true" PageSize="10">
                                <Columns>
                                    <RadzenDataGridColumn TItem="dynamic" Property="ScheduleName" Title="اسم الجدول" />
                                    <RadzenDataGridColumn TItem="dynamic" Property="BackupType" Title="نوع النسخة" />
                                    <RadzenDataGridColumn TItem="dynamic" Property="ScheduleType" Title="نوع الجدولة" />
                                    <RadzenDataGridColumn TItem="dynamic" Title="الحالة" Sortable="false">
                                        <Template Context="schedule">
                                            <RadzenBadge BadgeStyle="@(schedule.IsActive ? BadgeStyle.Success : BadgeStyle.Danger)"
                                                        Text="@(schedule.IsActive ? "نشط" : "غير نشط")" />
                                        </Template>
                                    </RadzenDataGridColumn>
                                    <RadzenDataGridColumn TItem="dynamic" Property="LastRunTime" Title="آخر تشغيل"
                                                         FormatString="{0:yyyy-MM-dd HH:mm}" />
                                    <RadzenDataGridColumn TItem="dynamic" Title="الإجراءات" Sortable="false" Filterable="false">
                                        <Template Context="schedule">
                                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem">
                                                <RadzenButton Icon="play_arrow" ButtonStyle="ButtonStyle.Success" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => RunScheduleNow(schedule.ScheduleId))" />
                                                <RadzenButton Icon="@(schedule.IsActive ? "pause" : "play_arrow")"
                                                             ButtonStyle="@(schedule.IsActive ? ButtonStyle.Warning : ButtonStyle.Success)"
                                                             Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => ToggleSchedule(schedule.ScheduleId))" />
                                                <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => EditSchedule(schedule))" />
                                                <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => DeleteSchedule(schedule.ScheduleId))" />
                                            </RadzenStack>
                                        </Template>
                                    </RadzenDataGridColumn>
                                </Columns>
                            </RadzenDataGrid>
                        }
                        else
                        {
                            <RadzenAlert AlertStyle="AlertStyle.Info" Text="لا توجد جداول مكونة" />
                        }
                    </RadzenCard>
                </RadzenStack>
            </RadzenTabsItem>

            <!-- Cloud Storage Tab -->
            <RadzenTabsItem Text="التخزين السحابي" Icon="cloud">
                <RadzenStack Gap="1rem" class="rz-mt-4">
                    <!-- Cloud Actions -->
                    <RadzenCard class="rz-p-4">
                        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween" AlignItems="AlignItems.Center">
                            <RadzenText TextStyle="TextStyle.H6">إعدادات التخزين السحابي</RadzenText>
                            <RadzenButton Text="حساب جديد" Icon="add" ButtonStyle="ButtonStyle.Primary"
                                         Click="@ShowCreateCloudConfigDialog" Size="ButtonSize.Medium" />
                        </RadzenStack>
                    </RadzenCard>

                    <!-- Cloud Configs Grid -->
                    <RadzenCard class="rz-p-4">
                        @if (cloudConfigs?.Any() == true)
                        {
                            <RadzenDataGrid Data="@cloudConfigs" TItem="CloudStorageConfigDto" AllowPaging="true" PageSize="10">
                                <Columns>
                                    <RadzenDataGridColumn TItem="CloudStorageConfigDto" Property="ConfigName" Title="اسم الحساب" />
                                    <RadzenDataGridColumn TItem="CloudStorageConfigDto" Property="ProviderName" Title="المزود" />
                                    <RadzenDataGridColumn TItem="CloudStorageConfigDto" Title="الحالة" Sortable="false">
                                        <Template Context="config">
                                            <RadzenBadge BadgeStyle="@(config.IsActive ? BadgeStyle.Success : BadgeStyle.Danger)"
                                                        Text="@(config.IsActive ? "نشط" : "غير نشط")" />
                                        </Template>
                                    </RadzenDataGridColumn>
                                    <RadzenDataGridColumn TItem="CloudStorageConfigDto" Property="CreatedDate" Title="تاريخ الإنشاء"
                                                         FormatString="{0:yyyy-MM-dd}" />
                                    <RadzenDataGridColumn TItem="CloudStorageConfigDto" Title="الإجراءات" Sortable="false" Filterable="false">
                                        <Template Context="config">
                                            <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem">
                                                <RadzenButton Icon="wifi" ButtonStyle="ButtonStyle.Info" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => TestConnection(config.ConfigId))" />
                                                <RadzenButton Icon="folder" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => ViewFiles(config.ConfigId))" />
                                                <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => EditCloudConfig(config))" />
                                                <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger" Size="ButtonSize.ExtraSmall"
                                                             Click="@(() => DeleteCloudConfig(config.ConfigId))" />
                                            </RadzenStack>
                                        </Template>
                                    </RadzenDataGridColumn>
                                </Columns>
                            </RadzenDataGrid>
                        }
                        else
                        {
                            <RadzenAlert AlertStyle="AlertStyle.Info" Text="لا توجد حسابات سحابية مكونة" />
                        }
                    </RadzenCard>

                    <!-- Cloud Uploads History -->
                    <RadzenCard class="rz-p-4">
                        <RadzenText TextStyle="TextStyle.H6" class="rz-mb-3">سجل الرفع السحابي</RadzenText>
                        @if (cloudUploads?.Any() == true)
                        {
                            <RadzenDataGrid Data="@cloudUploads" TItem="BackupCloudUploadDto" AllowPaging="true" PageSize="5">
                                <Columns>
                                    <RadzenDataGridColumn TItem="BackupCloudUploadDto" Property="LocalFileName" Title="اسم الملف" />
                                    <RadzenDataGridColumn TItem="BackupCloudUploadDto" Property="ProviderName" Title="المزود" />
                                    <RadzenDataGridColumn TItem="BackupCloudUploadDto" Property="UploadStatus" Title="الحالة" />
                                    <RadzenDataGridColumn TItem="BackupCloudUploadDto" Property="CreatedDate" Title="تاريخ الرفع"
                                                         FormatString="{0:yyyy-MM-dd HH:mm}" />
                                    <RadzenDataGridColumn TItem="BackupCloudUploadDto" Property="FileSizeBytes" Title="الحجم"
                                                         FormatString="{0:N0} بايت" />
                                </Columns>
                            </RadzenDataGrid>
                        }
                        else
                        {
                            <RadzenAlert AlertStyle="AlertStyle.Info" Text="لا يوجد سجل رفع سحابي" />
                        }
                    </RadzenCard>
                </RadzenStack>
            </RadzenTabsItem>
        </Tabs>
    </RadzenTabs>
</RadzenStack>
