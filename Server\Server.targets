<Project>
  <!-- حل مشكلة ملفات .pdb المحجوزة -->
  <PropertyGroup>
    <!-- تعطيل نسخ ملفات .pdb في Development -->
    <CopyDebugSymbolFilesFromPackages Condition="'$(Configuration)' == 'Debug'">false</CopyDebugSymbolFilesFromPackages>
    <CopyDocumentationFilesFromPackages>false</CopyDocumentationFilesFromPackages>
  </PropertyGroup>

  <!-- منع نسخ ملفات Client.pdb المحجوزة -->
  <Target Name="FilterClientPdbFiles" BeforeTargets="CopyFilesToOutputDirectory">
    <ItemGroup>
      <!-- إزالة ملفات .pdb الخاصة بـ Client من قائمة النسخ -->
      <ReferenceCopyLocalPaths Remove="@(ReferenceCopyLocalPaths)" 
                               Condition="'%(Filename)%(Extension)' == 'AppDev.Client.pdb'" />
    </ItemGroup>
  </Target>

  <!-- تجاهل أخطاء نسخ ملفات .pdb -->
  <Target Name="IgnorePdbCopyErrors" BeforeTargets="CopyFilesToOutputDirectory">
    <PropertyGroup>
      <ContinueOnError>true</ContinueOnError>
    </PropertyGroup>
  </Target>
</Project>
