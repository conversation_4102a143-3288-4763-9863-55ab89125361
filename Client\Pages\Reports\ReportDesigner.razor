@page "/report-designer"
@page "/report-designer/{ReportId:int}"
@page "/reports/designer"
@page "/reports/designer/{ReportId:int}"
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService
@inject NavigationManager Navigation

<PageTitle>مصمم التقارير</PageTitle>

<style>
    .designer-container {
        width: 100%;
        height: calc(100vh - 120px);
        min-height: 600px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }

    .toolbar {
        background: #f8f9fa;
        padding: 10px;
        border-bottom: 1px solid #ddd;
        display: flex;
        gap: 10px;
        align-items: center;
        flex-wrap: wrap;
    }

    .toolbar button {
        padding: 8px 16px;
        border: 1px solid #ccc;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }

    .toolbar button:hover {
        background: #e9ecef;
    }

    .toolbar button.primary {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }

    .toolbar button.primary:hover {
        background: #0056b3;
    }
</style>

<div class="designer-container">
    <!-- Toolbar -->
    <div class="toolbar">
        <button class="primary" @onclick="NewReport">
            <i class="fas fa-plus"></i> جديد
        </button>
        <button @onclick="OpenReport">
            <i class="fas fa-folder-open"></i> فتح
        </button>
        <button @onclick="SaveReport">
            <i class="fas fa-save"></i> حفظ
        </button>
        <button @onclick="SaveAsReport">
            <i class="fas fa-save"></i> حفظ باسم
        </button>
        <div style="border-left: 1px solid #ccc; height: 30px; margin: 0 10px;"></div>
        <button @onclick="PreviewReport">
            <i class="fas fa-eye"></i> معاينة
        </button>
        <button @onclick="ExportReport">
            <i class="fas fa-download"></i> تصدير
        </button>
        <div style="border-left: 1px solid #ccc; height: 30px; margin: 0 10px;"></div>
        <button @onclick="ShowSettings">
            <i class="fas fa-cog"></i> إعدادات
        </button>
    </div>

    <!-- Stimulsoft Designer Placeholder -->
    <div style="height: calc(100% - 60px);">
        <div
            style="display: flex; justify-content: center; align-items: center; height: 100%; flex-direction: column; background: #f8f9fa; border: 2px dashed #dee2e6;">
            <i class="fas fa-tools" style="font-size: 4rem; color: #6c757d; margin-bottom: 20px;"></i>
            <h4 style="color: #6c757d; margin-bottom: 15px;">مصمم التقارير Stimulsoft</h4>
            <p style="color: #6c757d; text-align: center; max-width: 500px;">
                لتفعيل مصمم التقارير، يرجى إضافة ترخيص Stimulsoft الصحيح في ملف Program.cs
            </p>
            <div
                style="background: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; font-family: monospace; font-size: 14px;">
                StiLicense.Key = "Your License Key";
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public int? ReportId { get; set; }

    private string currentReportName = "تقرير جديد";

    protected override async Task OnInitializedAsync()
    {
        if (ReportId.HasValue)
        {
            currentReportName = $"تقرير رقم {ReportId.Value}";
        }
        else
        {
            currentReportName = "تقرير جديد";
        }
    }

    private async Task NewReport()
    {
        Navigation.NavigateTo("/report-designer");
    }

    private async Task CreateNewReport()
    {
        Navigation.NavigateTo("/report-designer");
    }

    private async Task OpenReport()
    {
        Navigation.NavigateTo("/reports");
    }

    private async Task SaveReport()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "معلومات",
            Detail = "لحفظ التقارير، يرجى تفعيل ترخيص Stimulsoft أولاً"
        });
    }

    private async Task SaveAsReport()
    {
        await SaveReport();
    }

    private async Task PreviewReport()
    {
        if (ReportId.HasValue)
        {
            Navigation.NavigateTo($"/report-viewer/{ReportId.Value}");
        }
        else
        {
            Navigation.NavigateTo("/report-viewer/0");
        }
    }

    private async Task ExportReport()
    {
        await PreviewReport();
    }

    private async Task ShowSettings()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "معلومات",
            Detail = "إعدادات المصمم ستكون متاحة بعد تفعيل الترخيص"
        });
    }
}
}
