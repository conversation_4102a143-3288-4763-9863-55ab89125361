using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using AppDev.Server.Data;
using AppDev.Server.Models.AppDevDB;
using AppDev.Server.Services;
using System.Security.Claims;

namespace AppDev.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class ErrorLogController : ControllerBase
    {
        private readonly AppDevDBContext _context;
        private readonly IErrorLoggingService _errorLoggingService;

        public ErrorLogController(AppDevDBContext context, IErrorLoggingService errorLoggingService)
        {
            _context = context;
            _errorLoggingService = errorLoggingService;
        }

        [HttpGet("user-logs")]
        public async Task<ActionResult<IEnumerable<AppErrorLog>>> GetUserLogs(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string logLevel = null,
            [FromQuery] bool? isResolved = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var query = _context.AppErrorLogs.Where(log => log.UserId == userId);

                // Apply filters
                if (!string.IsNullOrEmpty(logLevel))
                {
                    query = query.Where(log => log.LogLevel == logLevel);
                }

                if (isResolved.HasValue)
                {
                    query = query.Where(log => log.IsResolved == isResolved.Value);
                }

                if (fromDate.HasValue)
                {
                    query = query.Where(log => log.CreatedDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(log => log.CreatedDate <= toDate.Value);
                }

                var totalCount = await query.CountAsync();
                var logs = await query
                    .OrderByDescending(log => log.CreatedDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                return Ok(logs);
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogErrorAsync("Failed to retrieve user logs", ex, "ErrorLogController.GetUserLogs");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("admin-logs")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<AppErrorLog>>> GetAllLogs(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string logLevel = null,
            [FromQuery] bool? isResolved = null,
            [FromQuery] string userId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var query = _context.AppErrorLogs.AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(logLevel))
                {
                    query = query.Where(log => log.LogLevel == logLevel);
                }

                if (isResolved.HasValue)
                {
                    query = query.Where(log => log.IsResolved == isResolved.Value);
                }

                if (!string.IsNullOrEmpty(userId))
                {
                    query = query.Where(log => log.UserId.Contains(userId));
                }

                if (fromDate.HasValue)
                {
                    query = query.Where(log => log.CreatedDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(log => log.CreatedDate <= toDate.Value);
                }

                var totalCount = await query.CountAsync();
                var logs = await query
                    .OrderByDescending(log => log.CreatedDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                Response.Headers.Append("X-Total-Count", totalCount.ToString());
                return Ok(logs);
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogErrorAsync("Failed to retrieve admin logs", ex, "ErrorLogController.GetAllLogs");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("resolve/{errorId}")]
        public async Task<ActionResult> ResolveError(long errorId, [FromBody] ResolveErrorRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var errorLog = await _context.AppErrorLogs.FindAsync(errorId);

                if (errorLog == null)
                {
                    return NotFound();
                }

                // Check if user can resolve this error (own error or admin)
                if (errorLog.UserId != userId && !User.IsInRole("Admin"))
                {
                    return Forbid();
                }

                var success = await _errorLoggingService.MarkAsResolvedAsync(errorId, request.ResolutionNotes);
                if (success)
                {
                    return Ok();
                }

                return BadRequest("Failed to resolve error");
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogErrorAsync("Failed to resolve error", ex, "ErrorLogController.ResolveError");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("statistics")]
        public async Task<ActionResult<ErrorStatistics>> GetStatistics()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var userLogs = _context.AppErrorLogs.Where(log => log.UserId == userId);
                var today = DateTime.UtcNow.Date;
                var weekAgo = today.AddDays(-7);
                var monthAgo = today.AddDays(-30);

                var statistics = new ErrorStatistics
                {
                    TotalErrors = await userLogs.CountAsync(),
                    UnresolvedErrors = await userLogs.Where(log => !log.IsResolved).CountAsync(),
                    ErrorsToday = await userLogs.Where(log => log.CreatedDate >= today).CountAsync(),
                    ErrorsThisWeek = await userLogs.Where(log => log.CreatedDate >= weekAgo).CountAsync(),
                    ErrorsThisMonth = await userLogs.Where(log => log.CreatedDate >= monthAgo).CountAsync(),
                    ErrorsByLevel = await userLogs
                        .GroupBy(log => log.LogLevel)
                        .Select(g => new ErrorLevelCount { Level = g.Key, Count = g.Count() })
                        .ToListAsync()
                };

                return Ok(statistics);
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogErrorAsync("Failed to get error statistics", ex, "ErrorLogController.GetStatistics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("cleanup")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult> ManualCleanup([FromQuery] int daysToKeep = 30)
        {
            try
            {
                await _errorLoggingService.CleanupOldLogsAsync(daysToKeep);
                return Ok($"Cleanup completed. Logs older than {daysToKeep} days have been removed.");
            }
            catch (Exception ex)
            {
                await _errorLoggingService.LogErrorAsync("Failed to perform manual cleanup", ex, "ErrorLogController.ManualCleanup");
                return StatusCode(500, "Internal server error");
            }
        }
    }

    public class ResolveErrorRequest
    {
        public string ResolutionNotes { get; set; }
    }

    public class ErrorStatistics
    {
        public int TotalErrors { get; set; }
        public int UnresolvedErrors { get; set; }
        public int ErrorsToday { get; set; }
        public int ErrorsThisWeek { get; set; }
        public int ErrorsThisMonth { get; set; }
        public List<ErrorLevelCount> ErrorsByLevel { get; set; } = new();
    }

    public class ErrorLevelCount
    {
        public string Level { get; set; }
        public int Count { get; set; }
    }
}
