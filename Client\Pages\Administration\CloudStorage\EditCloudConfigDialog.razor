@using AppDev.Server.Models
@using System.Text.Json
@inject HttpClient Http
@inject NotificationService NotificationService
@inject DialogService DialogService



<RadzenStack Gap="1rem">
    @if (isLoading)
    {
        <RadzenStack Gap="1rem" Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Center"
            AlignItems="AlignItems.Center">
            <RadzenProgressBarCircular ShowValue="false" Mode="ProgressBarMode.Indeterminate"
                Size="ProgressBarCircularSize.Medium" />
            <RadzenText Text="جاري التحميل..." TextStyle="TextStyle.Body1" />
        </RadzenStack>
    }
    else if (request != null)
    {
        <!-- Provider Selection -->
        <RadzenFieldset Text="مقدم الخدمة">
            <RadzenDropDown @bind-Value="@request.ProviderId" Data="@Providers" TextProperty="ProviderName"
                ValueProperty="ProviderId" Placeholder="اختر مقدم الخدمة" class="w-100" Change="@OnProviderChanged" />
        </RadzenFieldset>

        <!-- Basic Information -->
        <RadzenFieldset Text="المعلومات الأساسية">
            <RadzenStack Gap="1rem">
                <RadzenRow Gap="1rem">
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenLabel Text="اسم الإعدادات" />
                        <RadzenTextBox @bind-Value="@request.ConfigName" Placeholder="اسم الإعدادات" class="w-100" />
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenLabel Text="الوصف (اختياري)" />
                        <RadzenTextBox @bind-Value="@request.Description" Placeholder="وصف الإعدادات" class="w-100" />
                    </RadzenColumn>
                </RadzenRow>
            </RadzenStack>
        </RadzenFieldset>

        <!-- Authentication Settings -->
        @if (selectedProvider?.RequiresAuthentication == true)
        {
            <RadzenFieldset Text="إعدادات المصادقة">
                <RadzenStack Gap="1rem">
                    @if (selectedProvider.ProviderType == "GoogleDrive")
                    {
                        <RadzenRow Gap="1rem">
                            <RadzenColumn Size="12" SizeMD="6">
                                <RadzenLabel Text="Client ID" />
                                <RadzenTextBox @bind-Value="@request.ClientId" Placeholder="Google Client ID" class="w-100" />
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeMD="6">
                                <RadzenLabel Text="Client Secret" />
                                <RadzenPassword @bind-Value="@request.ClientSecret" Placeholder="Google Client Secret"
                                    class="w-100" />
                            </RadzenColumn>
                        </RadzenRow>
                    }
                    else if (selectedProvider.ProviderType == "Mega")
                    {
                        <RadzenRow Gap="1rem">
                            <RadzenColumn Size="12" SizeMD="6">
                                <RadzenLabel Text="اسم المستخدم" />
                                <RadzenTextBox @bind-Value="@request.Username" Placeholder="Mega Username" class="w-100" />
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeMD="6">
                                <RadzenLabel Text="كلمة المرور" />
                                <RadzenPassword @bind-Value="@request.Password" Placeholder="Mega Password" class="w-100" />
                            </RadzenColumn>
                        </RadzenRow>
                    }
                    else
                    {
                        <RadzenRow Gap="1rem">
                            <RadzenColumn Size="12" SizeMD="6">
                                <RadzenLabel Text="API Key" />
                                <RadzenPassword @bind-Value="@request.ApiKey" Placeholder="API Key" class="w-100" />
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeMD="6">
                                <RadzenLabel Text="اسم المستخدم (اختياري)" />
                                <RadzenTextBox @bind-Value="@request.Username" Placeholder="Username" class="w-100" />
                            </RadzenColumn>
                        </RadzenRow>
                    }
                </RadzenStack>
            </RadzenFieldset>
        }

        <!-- Storage Settings -->
        <RadzenFieldset Text="إعدادات التخزين">
            <RadzenStack Gap="1rem">
                <RadzenRow Gap="1rem">
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenLabel Text="مسار المجلد" />
                        <RadzenTextBox @bind-Value="@request.FolderPath" Placeholder="/AppDev/Backups" class="w-100" />
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenLabel Text="بادئة اسم الملف" />
                        <RadzenTextBox @bind-Value="@request.FileNamePrefix" Placeholder="AppDev_" class="w-100" />
                    </RadzenColumn>
                </RadzenRow>

                <RadzenRow Gap="1rem">
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenLabel Text="مدة الاحتفاظ (بالأيام)" />
                        <RadzenNumeric @bind-Value="@request.RetentionDays" Min="1" Max="365" class="w-100" />
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenStack Gap="0.5rem" class="rz-mt-4">
                            <RadzenCheckBox @bind-Value="@request.AutoUpload" Name="autoUpload" />
                            <RadzenLabel Text="رفع تلقائي" Component="autoUpload" />
                        </RadzenStack>
                    </RadzenColumn>
                </RadzenRow>

                <RadzenRow Gap="1rem">
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenStack Gap="0.5rem">
                            <RadzenCheckBox @bind-Value="@request.DeleteLocalAfterUpload" Name="deleteLocal" />
                            <RadzenLabel Text="حذف الملف المحلي بعد الرفع" Component="deleteLocal" />
                        </RadzenStack>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenStack Gap="0.5rem">
                            <RadzenCheckBox @bind-Value="@request.IsDefault" Name="isDefault" />
                            <RadzenLabel Text="تعيين كافتراضي" Component="isDefault" />
                        </RadzenStack>
                    </RadzenColumn>
                </RadzenRow>
            </RadzenStack>
        </RadzenFieldset>

        <!-- Notification Settings -->
        <RadzenFieldset Text="إعدادات الإشعارات">
            <RadzenStack Gap="1rem">
                <RadzenRow Gap="1rem">
                    <RadzenColumn Size="12" SizeMD="4">
                        <RadzenStack Gap="0.5rem">
                            <RadzenCheckBox @bind-Value="@request.NotifyOnSuccess" Name="notifySuccess" />
                            <RadzenLabel Text="إشعار عند النجاح" Component="notifySuccess" />
                        </RadzenStack>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="4">
                        <RadzenStack Gap="0.5rem">
                            <RadzenCheckBox @bind-Value="@request.NotifyOnFailure" Name="notifyFailure" />
                            <RadzenLabel Text="إشعار عند الفشل" Component="notifyFailure" />
                        </RadzenStack>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="4">
                        <RadzenLabel Text="البريد الإلكتروني (اختياري)" />
                        <RadzenTextBox @bind-Value="@request.NotificationEmail" Placeholder="<EMAIL>"
                            class="w-100" />
                    </RadzenColumn>
                </RadzenRow>
            </RadzenStack>
        </RadzenFieldset>

        <!-- Action Buttons -->
        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" JustifyContent="JustifyContent.End" class="rz-mt-4">
            <RadzenButton Text="إلغاء" Click="@Cancel" ButtonStyle="ButtonStyle.Light" />
            <RadzenButton Text="حفظ" Click="@Save" ButtonStyle="ButtonStyle.Primary" IsBusy="@isSaving" />
        </RadzenStack>
    }
</RadzenStack>

@code {
    [Parameter] public int ConfigId { get; set; }
    [Parameter] public List<CloudStorageProviderDto> Providers { get; set; } = new();

    private UpdateCloudStorageConfigRequest? request;
    private CloudStorageProviderDto? selectedProvider;
    private bool isLoading = true;
    private bool isSaving = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadConfiguration();
    }

    private async Task LoadConfiguration()
    {
        try
        {
            var response = await Http.GetAsync($"api/backup/cloud/configs/{ConfigId}");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var config = JsonSerializer.Deserialize<CloudStorageConfigDetailDto>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (config != null)
                {
                    request = new UpdateCloudStorageConfigRequest
                    {
                        ConfigId = config.ConfigId,
                        ProviderId = config.ProviderId,
                        ConfigName = config.ConfigName,
                        Description = config.Description,
                        ClientId = config.ClientId,
                        ClientSecret = config.ClientSecret,
                        ApiKey = config.ApiKey,
                        Username = config.Username,
                        Password = config.Password,
                        FolderPath = config.FolderPath,
                        FileNamePrefix = config.FileNamePrefix,
                        AutoUpload = config.AutoUpload,
                        DeleteLocalAfterUpload = config.DeleteLocalAfterUpload,
                        RetentionDays = config.RetentionDays,
                        NotifyOnSuccess = config.NotifyOnSuccess,
                        NotifyOnFailure = config.NotifyOnFailure,
                        NotificationEmail = config.NotificationEmail,
                        IsDefault = config.IsDefault
                    };

                    OnProviderChanged();
                }
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = $"فشل في تحميل الإعدادات: {ex.Message}",
                Duration = 4000
            });
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void OnProviderChanged()
    {
        if (request != null)
        {
            selectedProvider = Providers.FirstOrDefault(p => p.ProviderId == request.ProviderId);
        }
    }

    private async Task Save()
    {
        if (request == null) return;

        if (string.IsNullOrWhiteSpace(request.ConfigName))
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = "تحذير",
                Detail = "يرجى إدخال اسم الإعدادات",
                Duration = 4000
            });
            return;
        }

        if (request.ProviderId == 0)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = "تحذير",
                Detail = "يرجى اختيار مقدم الخدمة",
                Duration = 4000
            });
            return;
        }

        isSaving = true;
        StateHasChanged();

        try
        {
            var response = await Http.PutAsJsonAsync("api/backup/cloud/configs", request);
            if (response.IsSuccessStatusCode)
            {
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Success,
                    Summary = "نجح",
                    Detail = "تم تحديث الإعدادات بنجاح",
                    Duration = 4000
                });

                DialogService.Close(true);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent);

                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Error,
                    Summary = "خطأ",
                    Detail = errorResult.GetProperty("message").GetString() ?? "فشل في تحديث الإعدادات",
                    Duration = 4000
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = $"حدث خطأ: {ex.Message}",
                Duration = 4000
            });
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        DialogService.Close(false);
    }

    // Data models
    public class UpdateCloudStorageConfigRequest
    {
        public int ConfigId { get; set; }
        public int ProviderId { get; set; }
        public string ConfigName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? ClientId { get; set; }
        public string? ClientSecret { get; set; }
        public string? ApiKey { get; set; }
        public string? Username { get; set; }
        public string? Password { get; set; }
        public string? FolderPath { get; set; } = "/AppDev/Backups";
        public string? FileNamePrefix { get; set; } = "AppDev_";
        public bool AutoUpload { get; set; } = false;
        public bool DeleteLocalAfterUpload { get; set; } = false;
        public int? RetentionDays { get; set; } = 30;
        public bool NotifyOnSuccess { get; set; } = true;
        public bool NotifyOnFailure { get; set; } = true;
        public string? NotificationEmail { get; set; }
        public bool IsDefault { get; set; } = false;
    }

    public class CloudStorageConfigDetailDto
    {
        public int ConfigId { get; set; }
        public int ProviderId { get; set; }
        public string ConfigName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? ClientId { get; set; }
        public string? ClientSecret { get; set; }
        public string? ApiKey { get; set; }
        public string? Username { get; set; }
        public string? Password { get; set; }
        public string? FolderPath { get; set; }
        public string? FileNamePrefix { get; set; }
        public bool AutoUpload { get; set; }
        public bool DeleteLocalAfterUpload { get; set; }
        public int? RetentionDays { get; set; }
        public bool NotifyOnSuccess { get; set; }
        public bool NotifyOnFailure { get; set; }
        public string? NotificationEmail { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public DateTime? LastSyncDate { get; set; }
        public string? LastSyncStatus { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public string ProviderType { get; set; } = string.Empty;
    }

    public class CloudStorageProviderDto
    {
        public int ProviderId { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public string ProviderType { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? ApiEndpoint { get; set; }
        public bool IsActive { get; set; }
        public bool RequiresAuthentication { get; set; }
        public string? SupportedFileTypes { get; set; }
        public long? MaxFileSize { get; set; }
        public string? DocumentationUrl { get; set; }
    }
}
