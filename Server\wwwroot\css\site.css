#blazor-error-ui {
  background: lightyellow;
  bottom: 0;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
  display: none;
  left: 0;
  padding: 0.6rem 1.25rem 0.7rem 1.25rem;
  position: fixed;
  width: 100%;
  z-index: 1000;
}

#blazor-error-ui .dismiss {
  cursor: pointer;
  position: absolute;
  right: 0.75rem;
  top: 0.5rem;
}

:root {
  font-size: var(--rz-root-font-size);
  --app-primary-color: #667eea;
  --app-secondary-color: #764ba2;
  --app-success-color: #43e97b;
  --app-info-color: #4facfe;
  --app-warning-color: #f093fb;
  --app-danger-color: #f5576c;
  --app-light-color: #f8f9fa;
  --app-dark-color: #343a40;
  --app-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --app-shadow-hover: 0 8px 24px rgba(0, 0, 0, 0.15);
  --app-border-radius: 12px;
  --app-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  font-family: var(--rz-text-font-family);
  color: var(--rz-text-color);
  font-size: var(--rz-body-font-size);
  line-height: var(--rz-body-line-height);
  background-color: var(--rz-body-background-color);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.rz-body {
  --rz-body-padding: 0;
}

a {
  color: var(--rz-link-color);
}

a:hover,
a:focus {
  color: var(--rz-link-hover-color);
}

.blazor-error-boundary {
  background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=)
      no-repeat 1rem/1.8rem,
    #b32121;
  padding: 1rem 1rem 1rem 3.7rem;
  color: white;
}

.blazor-error-boundary::after {
  content: "An error has occurred.";
}

.loading-progress {
  position: relative;
  display: block;
  width: 8rem;
  height: 8rem;
  margin: 20vh auto 1rem auto;
}

.loading-progress circle {
  fill: none;
  stroke: #e0e0e0;
  stroke-width: 0.6rem;
  transform-origin: 50% 50%;
  transform: rotate(-90deg);
}

.loading-progress circle:last-child {
  stroke: #1b6ec2;
  stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
  transition: stroke-dasharray 0.05s ease-in-out;
}

.loading-progress-text {
  position: absolute;
  text-align: center;
  font-weight: bold;
  inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

.loading-progress-text:after {
  content: var(--blazor-load-percentage-text, "Loading");
}

/* Enhanced Professional Styling */
.rz-card {
    border-radius: var(--app-border-radius) !important;
    box-shadow: var(--app-shadow) !important;
    transition: var(--app-transition) !important;
    border: none !important;
    overflow: hidden;
}

.rz-card:hover {
    box-shadow: var(--app-shadow-hover) !important;
    transform: translateY(-2px);
}

.rz-button {
    border-radius: 8px !important;
    transition: var(--app-transition) !important;
    font-weight: 500 !important;
    text-transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.rz-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
}

.rz-button-primary {
    background: linear-gradient(135deg, var(--app-primary-color) 0%, var(--app-secondary-color) 100%) !important;
    border: none !important;
}

.rz-button-success {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    border: none !important;
}

.rz-button-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border: none !important;
}

.rz-button-secondary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
}

/* Enhanced Text Styling */
.rz-text-h3, .rz-text-h4, .rz-text-h5 {
    font-weight: 600 !important;
    letter-spacing: -0.025em;
}

/* Enhanced Badge Styling */
.rz-badge {
    border-radius: 20px !important;
    font-weight: 500 !important;
    padding: 4px 12px !important;
    font-size: 0.75rem !important;
}

/* Enhanced Icon Styling */
.rz-icon {
    transition: var(--app-transition) !important;
}

/* Enhanced Stack and Layout */
.rz-stack {
    gap: 1rem;
}

/* Professional Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: var(--app-border-radius);
    box-shadow: var(--app-shadow);
    transition: var(--app-transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.dashboard-card:hover {
    box-shadow: var(--app-shadow-hover);
    transform: translateY(-4px);
}

.dashboard-card-gradient {
    position: relative;
    overflow: hidden;
}

.dashboard-card-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

/* Enhanced Form Controls */
.rz-textbox, .rz-dropdown, .rz-datepicker {
    border-radius: 8px !important;
    border: 1px solid #e0e0e0 !important;
    transition: var(--app-transition) !important;
}

.rz-textbox:focus, .rz-dropdown:focus, .rz-datepicker:focus {
    border-color: var(--app-primary-color) !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* Enhanced Data Grid */
.rz-datatable {
    border-radius: var(--app-border-radius) !important;
    overflow: hidden;
    box-shadow: var(--app-shadow) !important;
}

.rz-datatable-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    font-weight: 600 !important;
}

/* Enhanced Sidebar */
.app-drawer {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
}

/* Enhanced Content Area */
.app-content {
    background: transparent !important;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .rz-card {
        margin-bottom: 1rem;
    }

    .dashboard-card:hover {
        transform: none;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced Notification Styling */
.rz-notification {
    border-radius: var(--app-border-radius) !important;
    box-shadow: var(--app-shadow-hover) !important;
    backdrop-filter: blur(10px);
}

/* Enhanced Dialog Styling */
.rz-dialog {
    border-radius: var(--app-border-radius) !important;
    box-shadow: var(--app-shadow-hover) !important;
    backdrop-filter: blur(10px);
}

/* Enhanced Progress Bar */
.rz-progressbar {
    border-radius: 10px !important;
    overflow: hidden;
}

/* Enhanced Tabs */
.rz-tabview-nav {
    border-radius: var(--app-border-radius) var(--app-border-radius) 0 0 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.rz-tabview-nav .rz-tabview-nav-link {
    border-radius: 8px 8px 0 0 !important;
    transition: var(--app-transition) !important;
}

.rz-tabview-nav .rz-tabview-nav-link.rz-tabview-selected {
    background: white !important;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
}
