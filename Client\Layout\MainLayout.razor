@inherits LayoutComponentBase
@inject CookieThemeService CookieThemeService
@inject Microsoft.Extensions.Localization.IStringLocalizer<MainLayout> L
<RadzenComponents />

<style>
    .app-bar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        height: 64px;
        display: flex;
        align-items: center;
        padding: 0 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
    }

    .app-drawer {
        position: fixed;
        top: 64px;
        left: 0;
        width: 280px;
        height: calc(100vh - 64px);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
        transform: translateX(
                @(sidebarExpanded
                                ? "0" :
                                "-280px")
            );
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 999;
        overflow-y: auto;
        border-right: 1px solid rgba(0, 0, 0, 0.05);
    }

    .app-content {
        margin-top: 64px;
        margin-left:
            @(sidebarExpanded
                        ?
                        "280px"
                        :
                        "0")
        ;
        transition: margin-left 0.3s ease;
        padding: 24px;
        min-height: calc(100vh - 64px);
        background: transparent;
        position: relative;
    }

    .app-content::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        z-index: -1;
    }

    .menu-button {
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        margin-right: 16px;
    }

    .menu-button:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .app-title {
        font-size: 20px;
        font-weight: 500;
        flex: 1;
    }

    .user-menu {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .theme-toggle {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
    }

    .theme-toggle:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .drawer-header {
        padding: 24px 16px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        position: relative;
        overflow: hidden;
    }

    .drawer-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        pointer-events: none;
    }

    .drawer-menu {
        padding: 8px 0;
    }

    .menu-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        color: #333;
        text-decoration: none;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        border-radius: 8px;
        margin: 2px 8px;
        position: relative;
        overflow: hidden;
    }

    .menu-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
    }

    .menu-item:hover {
        background: rgba(102, 126, 234, 0.05);
        color: #333;
        text-decoration: none;
        transform: translateX(4px);
    }

    .menu-item:hover::before {
        opacity: 1;
    }

    .menu-item.active {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        color: #667eea;
        border-left: 3px solid #667eea;
        font-weight: 500;
    }

    .menu-item-icon {
        margin-right: 12px;
        font-size: 20px;
    }

    .menu-group {
        margin: 8px 0;
    }

    .menu-group-title {
        padding: 8px 16px;
        font-size: 12px;
        font-weight: 600;
        color: #666;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 998;
        display:
            @(sidebarExpanded
                        &&
                        !isDesktop
                        ?
                        "block"
                        :
                        "none")
        ;
    }

    @@media (min-width: 1024px) {
        .overlay {
            display: none !important;
        }
    }
</style>

<!-- App Bar -->
<div class="app-bar">
    <button class="menu-button" @onclick="SidebarToggleClick">
        <RadzenIcon Icon="menu" />
    </button>

    <div class="app-title">AppDev</div>

    <div class="user-menu">
        <CulturePicker />

        <button class="theme-toggle" @onclick="ToggleTheme">
            <RadzenIcon Icon="brightness_6" />
        </button>

        @if (Security.IsAuthenticated())
        {
            <RadzenDropDown @bind-Value="selectedUserAction" Data="@userMenuItems" TextProperty="Text" ValueProperty="Value"
                Change="@OnUserMenuChange" Style="background: rgba(255,255,255,0.1); border: none; color: white;">
                <Template>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <RadzenGravatar Email="@Security.User?.Name" Style="width: 32px; height: 32px;" />
                        <span>@Security.User?.Name</span>
                        <RadzenIcon Icon="arrow_drop_down" />
                    </div>
                </Template>
            </RadzenDropDown>
        }
    </div>
</div>

<!-- Overlay for mobile -->
<div class="overlay" @onclick="SidebarToggleClick"></div>

<!-- Drawer -->
<div class="app-drawer">
    <div class="drawer-header">
        <RadzenImage Path="images/logo.png" style="width: 48px; height: 48px; margin-bottom: 8px;"
            AlternateText="AppDev Logo" />
        <div style="font-size: 18px; font-weight: 600; color: #333;">AppDev</div>
        <div style="font-size: 12px; color: #666;">نظام إدارة شامل</div>
    </div>

    <div class="drawer-menu">
        <!-- الرئيسية -->
        <a href="/" class="menu-item @(IsActive("/"))">
            <RadzenIcon Icon="home" class="menu-item-icon" />
            الرئيسية
        </a>

        <!-- التقارير -->
        <div class="menu-group">
            <div class="menu-group-title">التقارير</div>
            <a href="/reports" class="menu-item @(IsActive("/reports"))">
                <RadzenIcon Icon="assessment" class="menu-item-icon" />
                قائمة التقارير
            </a>
            <a href="/report-designer" class="menu-item @(IsActive("/report-designer"))">
                <RadzenIcon Icon="design_services" class="menu-item-icon" />
                مصمم التقارير
            </a>
            <a href="/report-designs" class="menu-item @(IsActive("/report-designs"))">
                <RadzenIcon Icon="palette" class="menu-item-icon" />
                تصميمات التقارير
            </a>
            <a href="/print-settings" class="menu-item @(IsActive("/print-settings"))">
                <RadzenIcon Icon="print" class="menu-item-icon" />
                إعدادات الطباعة
            </a>
            <a href="/report-permissions" class="menu-item @(IsActive("/report-permissions"))">
                <RadzenIcon Icon="security" class="menu-item-icon" />
                صلاحيات التقارير
            </a>
        </div>

        <!-- إدارة المستخدمين -->
        <div class="menu-group">
            <div class="menu-group-title">إدارة المستخدمين</div>
            <a href="/administration/users" class="menu-item @(IsActive("/administration/users"))">
                <RadzenIcon Icon="person" class="menu-item-icon" />
                المستخدمون
            </a>
            <a href="/administration/roles" class="menu-item @(IsActive("/administration/roles"))">
                <RadzenIcon Icon="admin_panel_settings" class="menu-item-icon" />
                الأدوار
            </a>
        </div>

        <!-- إدارة النظام -->
        <div class="menu-group">
            <div class="menu-group-title">إدارة النظام</div>
            <a href="/database-maintenance" class="menu-item @(IsActive("/database-maintenance"))">
                <RadzenIcon Icon="storage" class="menu-item-icon" />
                صيانة قاعدة البيانات
            </a>
            <a href="/admin/database/backup-management"
                class="menu-item @(IsActive("/admin/database/backup-management"))">
                <RadzenIcon Icon="backup" class="menu-item-icon" />
                النسخ الاحتياطي
            </a>
            <a href="/admin/cloudstorage/cloud-storage-config"
                class="menu-item @(IsActive("/admin/cloudstorage/cloud-storage-config"))">
                <RadzenIcon Icon="cloud" class="menu-item-icon" />
                التخزين السحابي
            </a>
            <a href="/admin/logs/error-logs" class="menu-item @(IsActive("/admin/logs/error-logs"))">
                <RadzenIcon Icon="error" class="menu-item-icon" />
                سجل الأخطاء
            </a>
        </div>
    </div>

    <!-- Footer -->
    <div
        style="position: absolute; bottom: 0; left: 0; right: 0; padding: 16px; border-top: 1px solid #e0e0e0; text-align: center; background: white;">
        <div style="font-size: 12px; color: #666;">AppDev v1.0.0</div>
        <div style="font-size: 11px; color: #999;">Copyright © 2025</div>
    </div>
</div>

<!-- Main Content -->
<div class="app-content">
    @Body
</div>
