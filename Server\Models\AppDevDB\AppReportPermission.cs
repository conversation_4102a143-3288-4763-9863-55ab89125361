using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AppDev.Server.Models.AppDevDB
{
    [Table("App_ReportPermissions")]
    public partial class AppReportPermission
    {

        [NotMapped]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("@odata.etag")]
        public string ETag
        {
            get;
            set;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int PermissionId { get; set; }

        [Required]
        [ConcurrencyCheck]
        public int ReportId { get; set; }

        public AppReport Report { get; set; }

        [Required]
        [ConcurrencyCheck]
        public string UserId { get; set; }

        [ConcurrencyCheck]
        public string RoleId { get; set; }

        [ConcurrencyCheck]
        public bool CanView { get; set; }

        [ConcurrencyCheck]
        public bool CanEdit { get; set; }

        [ConcurrencyCheck]
        public bool CanDelete { get; set; }

        [ConcurrencyCheck]
        public bool CanPrint { get; set; }

        [ConcurrencyCheck]
        public bool CanExport { get; set; }

        [ConcurrencyCheck]
        public bool CanShare { get; set; }

        [Required]
        [ConcurrencyCheck]
        public DateTime CreatedDate { get; set; }

        [ConcurrencyCheck]
        public DateTime? LastModifiedDate { get; set; }

        [ConcurrencyCheck]
        public string CreatedByUserId { get; set; }

        [ConcurrencyCheck]
        public string LastModifiedByUserId { get; set; }
    }
}
