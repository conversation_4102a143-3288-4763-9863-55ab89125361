using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AppDev.Server.Models.AppDevDB
{
    [Table("App_BackupCloudUploads")]
    public class AppBackupCloudUpload
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int UploadId { get; set; }

        [Required]
        public int ConfigId { get; set; }

        [Required]
        public int ProviderId { get; set; }

        [Required]
        [StringLength(500)]
        public string LocalFilePath { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string LocalFileName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? CloudFilePath { get; set; }

        [Required]
        [StringLength(200)]
        public string CloudFileName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? CloudFileId { get; set; }

        [StringLength(500)]
        public string? CloudFileUrl { get; set; }

        [Required]
        [StringLength(50)]
        public string BackupType { get; set; } = string.Empty; // JSON, SQL, Manual

        [Required]
        public long FileSizeBytes { get; set; }

        [StringLength(100)]
        public string? FileChecksum { get; set; }

        [Required]
        [StringLength(50)]
        public string UploadStatus { get; set; } = "Pending"; // Pending, InProgress, Completed, Failed, Cancelled

        public int UploadProgress { get; set; } = 0; // 0-100

        public DateTime? UploadStartTime { get; set; }

        public DateTime? UploadEndTime { get; set; }

        public TimeSpan? UploadDuration { get; set; }

        [StringLength(1000)]
        public string? ErrorMessage { get; set; }

        public int RetryCount { get; set; } = 0;

        public int MaxRetries { get; set; } = 3;

        public DateTime? NextRetryTime { get; set; }

        // Metadata
        [StringLength(50)]
        public string? CompressionType { get; set; }

        [StringLength(50)]
        public string? EncryptionType { get; set; }

        public bool IsEncrypted { get; set; } = false;

        public bool IsCompressed { get; set; } = false;

        [StringLength(1000)]
        public string? Tags { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Lifecycle
        public DateTime? ExpiryDate { get; set; }

        public bool IsDeleted { get; set; } = false;

        public DateTime? DeletedDate { get; set; }

        [StringLength(450)]
        public string? DeletedByUserId { get; set; }

        // Audit
        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        public DateTime? LastModifiedDate { get; set; }

        [StringLength(450)]
        public string? CreatedByUserId { get; set; }

        [StringLength(450)]
        public string? LastModifiedByUserId { get; set; }

        // Navigation Properties
        [ForeignKey("ConfigId")]
        public virtual AppCloudStorageConfig Config { get; set; } = null!;

        [ForeignKey("ProviderId")]
        public virtual AppCloudStorageProvider Provider { get; set; } = null!;
    }
}
