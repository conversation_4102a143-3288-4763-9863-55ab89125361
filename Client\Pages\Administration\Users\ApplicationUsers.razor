@page "/application-users"
@page "/administration/users"
@inject NotificationService NotificationService
@inject NavigationManager Navigation

<PageTitle>إدارة المستخدمين</PageTitle>

<RadzenStack Gap="1rem">
    <RadzenRow>
        <RadzenColumn Size="12">
            <RadzenText TextStyle="TextStyle.H3" TagName="TagName.H1" class="rz-pt-8">
                <RadzenIcon Icon="group" class="rz-me-1" />
                إدارة المستخدمين
            </RadzenText>
        </RadzenColumn>
    </RadzenRow>

    <!-- Statistics Cards -->
    <RadzenRow Gap="1rem">
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4"
                Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="group" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">0</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">إجمالي المستخدمين</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4"
                Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="verified_user" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">0</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">مؤكدو البريد</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4"
                Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="admin_panel_settings" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">0</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">لديهم أدوار</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4"
                Style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="today" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">0</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">مسجلون اليوم</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
    </RadzenRow>

    <!-- Action Buttons -->
    <RadzenCard class="rz-p-4">
        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" Wrap="FlexWrap.Wrap">
            <RadzenButton Text="إضافة مستخدم جديد" Icon="person_add" ButtonStyle="ButtonStyle.Primary"
                Click="@CreateNewUser" />
            <RadzenButton Text="تصدير المستخدمين" Icon="download" ButtonStyle="ButtonStyle.Secondary"
                Click="@ExportUsers" />
            <RadzenButton Text="إدارة الأدوار" Icon="admin_panel_settings" ButtonStyle="ButtonStyle.Info"
                Click="@NavigateToRoles" />
        </RadzenStack>
    </RadzenCard>

    <!-- Coming Soon Message -->
    <RadzenCard class="rz-p-4 rz-text-align-center">
        <RadzenIcon Icon="construction" Style="font-size: 4rem; color: var(--rz-warning);" />
        <RadzenText TextStyle="TextStyle.H5" class="rz-mt-3">نظام إدارة المستخدمين قيد التطوير</RadzenText>
        <RadzenText TextStyle="TextStyle.Body1" class="rz-mt-2">
            سيتم إضافة جميع ميزات إدارة المستخدمين والأدوار قريباً
        </RadzenText>
    </RadzenCard>
</RadzenStack>

@code {

    private async Task CreateNewUser()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "معلومات",
            Detail = "ميزة إضافة المستخدمين ستكون متاحة قريباً"
        });
    }

    private async Task ExportUsers()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "معلومات",
            Detail = "ميزة تصدير المستخدمين ستكون متاحة قريباً"
        });
    }

    private async Task NavigateToRoles()
    {
        Navigation.NavigateTo("/application-roles");
    }
}
