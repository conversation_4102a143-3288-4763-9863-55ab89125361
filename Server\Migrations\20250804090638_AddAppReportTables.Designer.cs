﻿// <auto-generated />
using System;
using AppDev.Server.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AppDev.Server.Migrations
{
    [DbContext(typeof(AppDevDBContext))]
    [Migration("20250804090638_AddAppReportTables")]
    partial class AddAppReportTables
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReport", b =>
                {
                    b.Property<int>("ReportId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("CreatedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActive")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastModifiedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("LastModifiedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ReportDataSource")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("ReportDescription")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("ReportMetadata")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("ReportName")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("ReportId");

                    b.ToTable("App_Reports", t =>
                        {
                            t.HasTrigger("App_Reports_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportDesign", b =>
                {
                    b.Property<int>("DesignId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("CreatedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("DesignCreatedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("DesignIsActive")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("DesignLastModified")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DesignName")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("DesignVersion")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("LastModifiedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<int?>("PrintSettingId")
                        .IsConcurrencyToken()
                        .HasColumnType("int");

                    b.Property<byte[]>("ReportDefinition")
                        .IsConcurrencyToken()
                        .HasColumnType("longblob");

                    b.Property<int?>("ReportId")
                        .IsConcurrencyToken()
                        .HasColumnType("int");

                    b.Property<string>("ReportPath")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.HasKey("DesignId");

                    b.HasIndex("PrintSettingId");

                    b.HasIndex("ReportId");

                    b.ToTable("App_ReportDesigns", t =>
                        {
                            t.HasTrigger("App_ReportDesigns_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportPermission", b =>
                {
                    b.Property<int>("PermissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<bool>("CanDelete")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanEdit")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanExport")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanPrint")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanShare")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanView")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CreatedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<int>("ReportId")
                        .IsConcurrencyToken()
                        .HasColumnType("int");

                    b.Property<string>("RoleId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("UserId")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("PermissionId");

                    b.HasIndex("ReportId");

                    b.ToTable("App_ReportPermissions", t =>
                        {
                            t.HasTrigger("App_ReportPermissions_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportPrintSetting", b =>
                {
                    b.Property<int>("PrintSettingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("CreatedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("FooterText")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("HeaderText")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<bool>("IsActive")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastModifiedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<decimal?>("MarginBottom")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal?>("MarginLeft")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal?>("MarginRight")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal?>("MarginTop")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("Orientation")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("PaperSize")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<bool?>("PrintFooter")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("PrintHeader")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("SettingCreatedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("SettingLastModified")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<string>("SettingName")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("PrintSettingId");

                    b.ToTable("App_ReportPrintSettings", t =>
                        {
                            t.HasTrigger("App_ReportPrintSettings_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportDesign", b =>
                {
                    b.HasOne("AppDev.Server.Models.AppDevDB.AppReportPrintSetting", "PrintSetting")
                        .WithMany("AppReportDesigns")
                        .HasForeignKey("PrintSettingId");

                    b.HasOne("AppDev.Server.Models.AppDevDB.AppReport", "Report")
                        .WithMany("AppReportDesigns")
                        .HasForeignKey("ReportId");

                    b.Navigation("PrintSetting");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportPermission", b =>
                {
                    b.HasOne("AppDev.Server.Models.AppDevDB.AppReport", "Report")
                        .WithMany("AppReportPermissions")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Report");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReport", b =>
                {
                    b.Navigation("AppReportDesigns");

                    b.Navigation("AppReportPermissions");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportPrintSetting", b =>
                {
                    b.Navigation("AppReportDesigns");
                });
#pragma warning restore 612, 618
        }
    }
}
