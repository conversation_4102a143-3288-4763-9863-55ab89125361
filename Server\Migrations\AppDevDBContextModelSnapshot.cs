﻿// <auto-generated />
using System;
using AppDev.Server.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace AppDev.Server.Migrations
{
    [DbContext(typeof(AppDevDBContext))]
    partial class AppDevDBContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppBackupCloudUpload", b =>
                {
                    b.Property<int>("UploadId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("BackupType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("CloudFileId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CloudFileName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("CloudFilePath")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("CloudFileUrl")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("CompressionType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("ConfigId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DeletedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("EncryptionType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<DateTime?>("ExpiryDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("FileChecksum")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<long>("FileSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsCompressed")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsEncrypted")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastModifiedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("LastModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LocalFileName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("LocalFilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("int");

                    b.Property<DateTime?>("NextRetryTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Notes")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<int>("RetryCount")
                        .HasColumnType("int");

                    b.Property<string>("Tags")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<TimeSpan?>("UploadDuration")
                        .HasColumnType("time(6)");

                    b.Property<DateTime?>("UploadEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("UploadProgress")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UploadStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UploadStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("UploadId");

                    b.HasIndex("ConfigId");

                    b.HasIndex("ProviderId");

                    b.ToTable("App_BackupCloudUploads", t =>
                        {
                            t.HasTrigger("App_BackupCloudUploads_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppBackupSchedule", b =>
                {
                    b.Property<int>("ScheduleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<TimeSpan?>("AverageRunDuration")
                        .HasColumnType("time(6)");

                    b.Property<string>("BackupType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int?>("CloudConfigId")
                        .HasColumnType("int");

                    b.Property<int>("CloudRetentionDays")
                        .HasColumnType("int");

                    b.Property<bool>("CompressBackups")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("CronExpression")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CustomSettings")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<int?>("DayOfMonth")
                        .HasColumnType("int");

                    b.Property<string>("DayOfWeek")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("DeleteLocalAfterUpload")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("EncryptBackups")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("EncryptionKey")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int>("FailedRuns")
                        .HasColumnType("int");

                    b.Property<int>("IntervalHours")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastModifiedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("LastModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<TimeSpan?>("LastRunDuration")
                        .HasColumnType("time(6)");

                    b.Property<string>("LastRunMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("LastRunStatus")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("LastRunTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("LocalRetentionDays")
                        .HasColumnType("int");

                    b.Property<int>("MaxCloudBackups")
                        .HasColumnType("int");

                    b.Property<int>("MaxLocalBackups")
                        .HasColumnType("int");

                    b.Property<DateTime?>("NextRunTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("NotificationEmail")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("NotificationWebhook")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<bool>("NotifyOnFailure")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NotifyOnSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ScheduleName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<TimeSpan?>("ScheduleTime")
                        .HasColumnType("time(6)");

                    b.Property<string>("ScheduleType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<int>("SuccessfulRuns")
                        .HasColumnType("int");

                    b.Property<bool>("UploadToCloud")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("VerifyIntegrity")
                        .HasColumnType("tinyint(1)");

                    b.HasKey("ScheduleId");

                    b.HasIndex("CloudConfigId");

                    b.ToTable("App_BackupSchedules", t =>
                        {
                            t.HasTrigger("App_BackupSchedules_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppCloudStorageConfig", b =>
                {
                    b.Property<int>("ConfigId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("AccessToken")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)");

                    b.Property<string>("ApiKey")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("AutoUpload")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("ClientSecret")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("ConfigName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("DeleteLocalAfterUpload")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("EncryptedPassword")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("FileNamePrefix")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("FolderPath")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)");

                    b.Property<string>("LastModifiedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("LastModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("LastSyncDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LastSyncStatus")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<long?>("MaxStorageBytes")
                        .HasColumnType("bigint");

                    b.Property<string>("NotificationEmail")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("NotifyOnFailure")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("NotifyOnSuccess")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("ProviderId")
                        .HasColumnType("int");

                    b.Property<string>("RefreshToken")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)");

                    b.Property<int?>("RetentionDays")
                        .HasColumnType("int");

                    b.Property<string>("Username")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.HasKey("ConfigId");

                    b.HasIndex("ProviderId");

                    b.ToTable("App_CloudStorageConfigs", t =>
                        {
                            t.HasTrigger("App_CloudStorageConfigs_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppCloudStorageProvider", b =>
                {
                    b.Property<int>("ProviderId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("ApiEndpoint")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastModifiedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("LastModifiedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<long?>("MaxFileSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<string>("ProviderName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ProviderType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<bool>("RequiresAuthentication")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("SupportedFileTypes")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.HasKey("ProviderId");

                    b.ToTable("App_CloudStorageProviders", t =>
                        {
                            t.HasTrigger("App_CloudStorageProviders_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppErrorLog", b =>
                {
                    b.Property<long>("ErrorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<string>("AdditionalData")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("ApplicationName")
                        .IsConcurrencyToken()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<DateTime>("CreatedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Exception")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("HttpMethod")
                        .IsConcurrencyToken()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("IpAddress")
                        .IsConcurrencyToken()
                        .HasMaxLength(45)
                        .HasColumnType("varchar(45)");

                    b.Property<bool>("IsResolved")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LogLevel")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("MachineName")
                        .IsConcurrencyToken()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Message")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("RequestPath")
                        .IsConcurrencyToken()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("ResolutionNotes")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("ResolvedByUserId")
                        .IsConcurrencyToken()
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.Property<DateTime?>("ResolvedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Source")
                        .IsConcurrencyToken()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("UserAgent")
                        .IsConcurrencyToken()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("UserId")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(450)
                        .HasColumnType("varchar(450)");

                    b.HasKey("ErrorId");

                    b.ToTable("App_ErrorLogs", t =>
                        {
                            t.HasTrigger("App_ErrorLogs_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReport", b =>
                {
                    b.Property<int>("ReportId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("CreatedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActive")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastModifiedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("LastModifiedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ReportDataSource")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("ReportDescription")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("ReportMetadata")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("ReportName")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("ReportId");

                    b.ToTable("App_Reports", t =>
                        {
                            t.HasTrigger("App_Reports_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportDesign", b =>
                {
                    b.Property<int>("DesignId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("CreatedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("DesignCreatedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("DesignIsActive")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("DesignLastModified")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<string>("DesignName")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("DesignVersion")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("LastModifiedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<int?>("PrintSettingId")
                        .IsConcurrencyToken()
                        .HasColumnType("int");

                    b.Property<byte[]>("ReportDefinition")
                        .IsConcurrencyToken()
                        .HasColumnType("longblob");

                    b.Property<int?>("ReportId")
                        .IsConcurrencyToken()
                        .HasColumnType("int");

                    b.Property<string>("ReportPath")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.HasKey("DesignId");

                    b.HasIndex("PrintSettingId");

                    b.HasIndex("ReportId");

                    b.ToTable("App_ReportDesigns", t =>
                        {
                            t.HasTrigger("App_ReportDesigns_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportPermission", b =>
                {
                    b.Property<int>("PermissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<bool>("CanDelete")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanEdit")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanExport")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanPrint")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanShare")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("CanView")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("CreatedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LastModifiedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("LastModifiedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<int>("ReportId")
                        .IsConcurrencyToken()
                        .HasColumnType("int");

                    b.Property<string>("RoleId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("UserId")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("PermissionId");

                    b.HasIndex("ReportId");

                    b.ToTable("App_ReportPermissions", t =>
                        {
                            t.HasTrigger("App_ReportPermissions_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportPrintSetting", b =>
                {
                    b.Property<int>("PrintSettingId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("CreatedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("FooterText")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("HeaderText")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<bool>("IsActive")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("LastModifiedByUserId")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<decimal?>("MarginBottom")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal?>("MarginLeft")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal?>("MarginRight")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal?>("MarginTop")
                        .IsConcurrencyToken()
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("Orientation")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<string>("PaperSize")
                        .IsConcurrencyToken()
                        .HasColumnType("longtext");

                    b.Property<bool?>("PrintFooter")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<bool?>("PrintHeader")
                        .IsConcurrencyToken()
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime>("SettingCreatedDate")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("SettingLastModified")
                        .IsConcurrencyToken()
                        .HasColumnType("datetime(6)");

                    b.Property<string>("SettingName")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("PrintSettingId");

                    b.ToTable("App_ReportPrintSettings", t =>
                        {
                            t.HasTrigger("App_ReportPrintSettings_Trigger");
                        });
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppBackupCloudUpload", b =>
                {
                    b.HasOne("AppDev.Server.Models.AppDevDB.AppCloudStorageConfig", "Config")
                        .WithMany("BackupCloudUploads")
                        .HasForeignKey("ConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AppDev.Server.Models.AppDevDB.AppCloudStorageProvider", "Provider")
                        .WithMany("BackupCloudUploads")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Config");

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppBackupSchedule", b =>
                {
                    b.HasOne("AppDev.Server.Models.AppDevDB.AppCloudStorageConfig", "CloudConfig")
                        .WithMany("BackupSchedules")
                        .HasForeignKey("CloudConfigId");

                    b.Navigation("CloudConfig");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppCloudStorageConfig", b =>
                {
                    b.HasOne("AppDev.Server.Models.AppDevDB.AppCloudStorageProvider", "Provider")
                        .WithMany("CloudStorageConfigs")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportDesign", b =>
                {
                    b.HasOne("AppDev.Server.Models.AppDevDB.AppReportPrintSetting", "PrintSetting")
                        .WithMany("AppReportDesigns")
                        .HasForeignKey("PrintSettingId");

                    b.HasOne("AppDev.Server.Models.AppDevDB.AppReport", "Report")
                        .WithMany("AppReportDesigns")
                        .HasForeignKey("ReportId");

                    b.Navigation("PrintSetting");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportPermission", b =>
                {
                    b.HasOne("AppDev.Server.Models.AppDevDB.AppReport", "Report")
                        .WithMany("AppReportPermissions")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Report");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppCloudStorageConfig", b =>
                {
                    b.Navigation("BackupCloudUploads");

                    b.Navigation("BackupSchedules");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppCloudStorageProvider", b =>
                {
                    b.Navigation("BackupCloudUploads");

                    b.Navigation("CloudStorageConfigs");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReport", b =>
                {
                    b.Navigation("AppReportDesigns");

                    b.Navigation("AppReportPermissions");
                });

            modelBuilder.Entity("AppDev.Server.Models.AppDevDB.AppReportPrintSetting", b =>
                {
                    b.Navigation("AppReportDesigns");
                });
#pragma warning restore 612, 618
        }
    }
}
