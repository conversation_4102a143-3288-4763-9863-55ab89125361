using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Radzen;
using AppDev.Client;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using System.Globalization;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.Services.AddRadzenComponents();
builder.Services.AddRadzenCookieThemeService(options =>
{
    options.Name = "AppDevTheme";
    options.Duration = TimeSpan.FromDays(365);
});
builder.Services.AddTransient(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });
builder.Services.AddScoped<AppDev.Client.AppDevDBService>();
builder.Services.AddAuthorizationCore();
builder.Services.AddHttpClient("AppDev.Server", client => client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress));
builder.Services.AddTransient(sp => sp.GetRequiredService<IHttpClientFactory>().CreateClient("AppDev.Server"));
builder.Services.AddScoped<AppDev.Client.SecurityService>();
builder.Services.AddScoped<AuthenticationStateProvider, AppDev.Client.ApplicationAuthenticationStateProvider>();
builder.Services.AddLocalization();
// Add Stimulsoft Services for Blazor
// Note: You need to add proper Stimulsoft license
// StiLicense.Key = "Your License Key";
// builder.Services.AddStimulsoftBlazor(); // Temporarily disabled until license is configured
var host = builder.Build();
var jsRuntime = host.Services.GetRequiredService<Microsoft.JSInterop.IJSRuntime>();
var culture = await jsRuntime.InvokeAsync<string>("Radzen.getCulture");
if (!string.IsNullOrEmpty(culture))
{
    CultureInfo.DefaultThreadCurrentCulture = new CultureInfo(culture);
    CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo(culture);
}

await host.RunAsync();