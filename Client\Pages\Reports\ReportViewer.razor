@page "/report-viewer/{ReportId:int}"
@page "/reports/viewer/{ReportId:int}"
@inject IJSRuntime JSRuntime
@inject NotificationService NotificationService
@inject NavigationManager Navigation

<PageTitle>عارض التقارير</PageTitle>

<style>
    .viewer-container {
        width: 100%;
        height: calc(100vh - 120px);
        min-height: 600px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }

    .toolbar {
        background: #f8f9fa;
        padding: 10px;
        border-bottom: 1px solid #ddd;
        display: flex;
        gap: 10px;
        align-items: center;
        flex-wrap: wrap;
    }

    .toolbar button {
        padding: 8px 16px;
        border: 1px solid #ccc;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }

    .toolbar button:hover {
        background: #e9ecef;
    }

    .toolbar button.primary {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }

    .toolbar button.primary:hover {
        background: #0056b3;
    }

    .report-info {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
    }
</style>

<div class="viewer-container">
    <!-- Report Info -->
    <div class="report-info">
        <h4 style="margin: 0 0 10px 0; color: #1976d2;">
            <i class="fas fa-file-alt"></i> @currentReportName
        </h4>
        <p style="margin: 0; color: #666;">
            معرف التقرير: @ReportId | تاريخ الإنشاء: @DateTime.Now.ToString("yyyy-MM-dd")
        </p>
    </div>

    <!-- Toolbar -->
    <div class="toolbar">
        <button class="primary" @onclick="RefreshReport">
            <i class="fas fa-sync"></i> تحديث
        </button>
        <button @onclick="PrintReport">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button @onclick="ExportToPdf">
            <i class="fas fa-file-pdf"></i> PDF
        </button>
        <button @onclick="ExportToExcel">
            <i class="fas fa-file-excel"></i> Excel
        </button>
        <button @onclick="ExportToWord">
            <i class="fas fa-file-word"></i> Word
        </button>
        <div style="border-left: 1px solid #ccc; height: 30px; margin: 0 10px;"></div>
        <button @onclick="EditReport">
            <i class="fas fa-edit"></i> تعديل
        </button>
        <button @onclick="BackToReports">
            <i class="fas fa-arrow-left"></i> العودة
        </button>
    </div>

    <!-- Stimulsoft Viewer Placeholder -->
    <div style="height: calc(100% - 120px);">
        <div
            style="display: flex; justify-content: center; align-items: center; height: 100%; flex-direction: column; background: #f8f9fa; border: 2px dashed #dee2e6;">
            <i class="fas fa-file-alt" style="font-size: 4rem; color: #6c757d; margin-bottom: 20px;"></i>
            <h4 style="color: #6c757d; margin-bottom: 15px;">عارض التقارير Stimulsoft</h4>
            <p style="color: #6c757d; text-align: center; max-width: 500px;">
                لعرض التقارير، يرجى إضافة ترخيص Stimulsoft الصحيح في ملف Program.cs
            </p>
            <div
                style="background: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; font-family: monospace; font-size: 14px;">
                StiLicense.Key = "Your License Key";
            </div>
            <div style="margin-top: 20px;">
                <button class="btn btn-primary" @onclick="BackToReports">
                    العودة إلى قائمة التقارير
                </button>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter] public int ReportId { get; set; }

    private string currentReportName = "تقرير";

    protected override async Task OnInitializedAsync()
    {
        currentReportName = ReportId > 0 ? $"تقرير رقم {ReportId}" : "تقرير تجريبي";
    }

    private async Task RefreshReport()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "معلومات",
            Detail = "لتحديث التقارير، يرجى تفعيل ترخيص Stimulsoft أولاً"
        });
    }

    private async Task PrintReport()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "معلومات",
            Detail = "لطباعة التقارير، يرجى تفعيل ترخيص Stimulsoft أولاً"
        });
    }

    private async Task ExportToPdf()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "تصدير",
            Detail = "لتصدير التقارير، يرجى تفعيل ترخيص Stimulsoft أولاً"
        });
    }

    private async Task ExportToExcel()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "تصدير",
            Detail = "لتصدير التقارير، يرجى تفعيل ترخيص Stimulsoft أولاً"
        });
    }

    private async Task ExportToWord()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "تصدير",
            Detail = "لتصدير التقارير، يرجى تفعيل ترخيص Stimulsoft أولاً"
        });
    }

    private async Task EditReport()
    {
        Navigation.NavigateTo($"/report-designer/{ReportId}");
    }

    private async Task BackToReports()
    {
        Navigation.NavigateTo("/reports");
    }
}
}