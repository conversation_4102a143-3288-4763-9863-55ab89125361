{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"AppDevDBConnection": "Server=127.0.0.1;Uid=flydev;Pwd=*********;Connection Timeout=60;Database=AppDevDB"}, "Databases": {"AppDevDB": {"NoPluralize": false, "UseDatabaseNames": false, "UseEFNaming": true, "PrefixNavigation": true}}, "BackupScheduler": {"Enabled": true, "IntervalHours": 24, "MaxBackupsToKeep": 10, "BackupType": "JSON", "EnableEncryption": true}}