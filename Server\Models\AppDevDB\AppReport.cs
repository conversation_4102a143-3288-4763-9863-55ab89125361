using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AppDev.Server.Models.AppDevDB
{
    [Table("App_Reports")]
    public partial class AppReport
    {

        [NotMapped]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("@odata.etag")]
        public string ETag
        {
            get;
            set;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ReportId { get; set; }

        [Required]
        [ConcurrencyCheck]
        public string ReportName { get; set; }

        [ConcurrencyCheck]
        public string ReportDescription { get; set; }

        [ConcurrencyCheck]
        public string ReportDataSource { get; set; }

        [ConcurrencyCheck]
        public string ReportMetadata { get; set; }

        [ConcurrencyCheck]
        public string CreatedByUserId { get; set; }

        [ConcurrencyCheck]
        public string LastModifiedByUserId { get; set; }

        [Required]
        [ConcurrencyCheck]
        public DateTime CreatedDate { get; set; }

        [ConcurrencyCheck]
        public DateTime? LastModifiedDate { get; set; }

        [ConcurrencyCheck]
        public bool IsActive { get; set; }

        public ICollection<AppReportDesign> AppReportDesigns { get; set; }

        public ICollection<AppReportPermission> AppReportPermissions { get; set; }
    }
}
