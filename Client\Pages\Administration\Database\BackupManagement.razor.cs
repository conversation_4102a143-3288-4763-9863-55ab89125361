#nullable enable
using Microsoft.AspNetCore.Components;
using AppDev.Server.Models;
using Ra<PERSON>zen;
using System.Text.Json;
using System.Net.Http.Json;
using Microsoft.JSInterop;

namespace AppDev.Client.Pages.Administration.Database
{
    public partial class BackupManagement : ComponentBase
    {
        [Inject] protected HttpClient Http { get; set; } = default!;
        [Inject] protected NotificationService NotificationService { get; set; } = default!;
        [Inject] protected DialogService DialogService { get; set; } = default!;
        [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;

        // Data properties
        private BackupDashboardData? dashboardData;
        private List<BackupInfo> backupFiles = new();
        private List<dynamic> schedules = new();
        private List<CloudStorageConfigDto> cloudConfigs = new();
        private List<BackupCloudUploadDto> cloudUploads = new();

        // UI state
        private int selectedTabIndex = 0;
        private bool isRefreshing = false;

        protected override async Task OnInitializedAsync()
        {
            await RefreshAllData();
        }

        private async Task RefreshAllData()
        {
            isRefreshing = true;
            StateHasChanged();

            try
            {
                await Task.WhenAll(
                    LoadDashboardData(),
                    LoadBackupFiles(),
                    LoadSchedules(),
                    LoadCloudConfigs(),
                    LoadCloudUploads()
                );
            }
            catch (Exception ex)
            {
                ShowErrorNotification("خطأ في تحديث البيانات", ex.Message);
            }
            finally
            {
                isRefreshing = false;
                StateHasChanged();
            }
        }

        private async Task LoadDashboardData()
        {
            try
            {
                var response = await Http.GetAsync("api/backup/dashboard");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    dashboardData = JsonSerializer.Deserialize<BackupDashboardData>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}");
            }
        }

        private async Task LoadBackupFiles()
        {
            try
            {
                var response = await Http.GetAsync("api/backup/files");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    backupFiles = JsonSerializer.Deserialize<List<BackupInfo>>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    }) ?? new List<BackupInfo>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل ملفات النسخ الاحتياطي: {ex.Message}");
            }
        }

        private async Task LoadSchedules()
        {
            try
            {
                var response = await Http.GetAsync("api/backup/schedules");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    schedules = JsonSerializer.Deserialize<List<dynamic>>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    }) ?? new List<dynamic>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل الجداول: {ex.Message}");
            }
        }

        private async Task LoadCloudConfigs()
        {
            try
            {
                var response = await Http.GetAsync("api/backup/cloud/configs");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    cloudConfigs = JsonSerializer.Deserialize<List<CloudStorageConfigDto>>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    }) ?? new List<CloudStorageConfigDto>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل إعدادات السحابة: {ex.Message}");
            }
        }

        private async Task LoadCloudUploads()
        {
            try
            {
                var response = await Http.GetAsync("api/backup/cloud/uploads");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    cloudUploads = JsonSerializer.Deserialize<List<BackupCloudUploadDto>>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    }) ?? new List<BackupCloudUploadDto>();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل سجل الرفع السحابي: {ex.Message}");
            }
        }

        // Backup operations
        private async Task CreateBackup(string backupType)
        {
            try
            {
                var request = new
                {
                    BackupType = backupType,
                    UploadToCloud = false
                };

                var response = await Http.PostAsJsonAsync("api/backup/instant", request);
                if (response.IsSuccessStatusCode)
                {
                    ShowSuccessNotification("نجح إنشاء النسخة الاحتياطية", $"تم إنشاء نسخة احتياطية {backupType} بنجاح");
                    await LoadBackupFiles();
                    await LoadDashboardData();
                }
                else
                {
                    ShowErrorNotification("فشل إنشاء النسخة الاحتياطية", "حدث خطأ أثناء إنشاء النسخة الاحتياطية");
                }
            }
            catch (Exception ex)
            {
                ShowErrorNotification("خطأ", ex.Message);
            }
        }

        private async Task DownloadBackup(string fileName)
        {
            try
            {
                await JSRuntime.InvokeVoidAsync("open", $"api/backup/download/{fileName}", "_blank");
            }
            catch (Exception ex)
            {
                ShowErrorNotification("خطأ في التحميل", ex.Message);
            }
        }

        private async Task DeleteBackup(string fileName)
        {
            var confirmed = await DialogService.Confirm("هل أنت متأكد من حذف هذه النسخة الاحتياطية؟", "تأكيد الحذف",
                new ConfirmOptions() { OkButtonText = "نعم", CancelButtonText = "إلغاء" });

            if (confirmed == true)
            {
                try
                {
                    var response = await Http.DeleteAsync($"api/backup/delete/{fileName}");
                    if (response.IsSuccessStatusCode)
                    {
                        ShowSuccessNotification("تم الحذف", "تم حذف النسخة الاحتياطية بنجاح");
                        await LoadBackupFiles();
                        await LoadDashboardData();
                    }
                    else
                    {
                        ShowErrorNotification("فشل الحذف", "حدث خطأ أثناء حذف النسخة الاحتياطية");
                    }
                }
                catch (Exception ex)
                {
                    ShowErrorNotification("خطأ", ex.Message);
                }
            }
        }

        // Dialog methods
        private async Task ShowInstantBackupDialog()
        {
            await DialogService.OpenAsync<CreateInstantBackupDialog>("إنشاء نسخة احتياطية فورية",
                new Dictionary<string, object>(),
                new DialogOptions() { Width = "600px", Height = "500px" });

            await RefreshAllData();
        }

        private async Task ShowCloudUploadDialog()
        {
            await DialogService.OpenAsync<CloudUploadDialog>("رفع ملف للسحابة",
                new Dictionary<string, object>(),
                new DialogOptions() { Width = "600px", Height = "400px" });

            await LoadCloudUploads();
        }

        private async Task ShowCreateScheduleDialog()
        {
            await DialogService.OpenAsync<CreateBackupScheduleDialog>("إنشاء جدول نسخ احتياطي",
                new Dictionary<string, object>(),
                new DialogOptions() { Width = "800px", Height = "600px" });

            await LoadSchedules();
        }

        private async Task ShowCreateCloudConfigDialog()
        {
            await DialogService.OpenAsync<AppDev.Client.Pages.Administration.CloudStorage.CreateCloudConfigDialog>("إضافة حساب تخزين سحابي",
                new Dictionary<string, object>(),
                new DialogOptions() { Width = "600px", Height = "500px" });

            await LoadCloudConfigs();
        }

        // Utility methods
        private void ShowSuccessNotification(string summary, string detail)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Success,
                Summary = summary,
                Detail = detail,
                Duration = 4000
            });
        }

        private void ShowErrorNotification(string summary, string detail)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = summary,
                Detail = detail,
                Duration = 4000
            });
        }

        // Schedule operations
        private async Task RunScheduleNow(int scheduleId)
        {
            try
            {
                var response = await Http.PostAsync($"api/backup/schedules/{scheduleId}/run", null);
                if (response.IsSuccessStatusCode)
                {
                    ShowSuccessNotification("تم تشغيل الجدول", "تم تشغيل جدول النسخ الاحتياطي بنجاح");
                    await LoadSchedules();
                    await LoadDashboardData();
                }
                else
                {
                    ShowErrorNotification("فشل تشغيل الجدول", "حدث خطأ أثناء تشغيل الجدول");
                }
            }
            catch (Exception ex)
            {
                ShowErrorNotification("خطأ", ex.Message);
            }
        }

        private async Task ToggleSchedule(int scheduleId)
        {
            try
            {
                var response = await Http.PostAsync($"api/backup/schedules/{scheduleId}/toggle", null);
                if (response.IsSuccessStatusCode)
                {
                    ShowSuccessNotification("تم تغيير حالة الجدول", "تم تغيير حالة الجدول بنجاح");
                    await LoadSchedules();
                    await LoadDashboardData();
                }
                else
                {
                    ShowErrorNotification("فشل تغيير الحالة", "حدث خطأ أثناء تغيير حالة الجدول");
                }
            }
            catch (Exception ex)
            {
                ShowErrorNotification("خطأ", ex.Message);
            }
        }

        private async Task EditSchedule(dynamic schedule)
        {
            await DialogService.OpenAsync<EditBackupScheduleDialog>("تعديل جدول النسخ الاحتياطي",
                new Dictionary<string, object> { { "Schedule", schedule } },
                new DialogOptions() { Width = "800px", Height = "600px" });

            await LoadSchedules();
        }

        private async Task DeleteSchedule(int scheduleId)
        {
            var confirmed = await DialogService.Confirm("هل أنت متأكد من حذف هذا الجدول؟", "تأكيد الحذف",
                new ConfirmOptions() { OkButtonText = "نعم", CancelButtonText = "إلغاء" });

            if (confirmed == true)
            {
                try
                {
                    var response = await Http.DeleteAsync($"api/backup/schedules/{scheduleId}");
                    if (response.IsSuccessStatusCode)
                    {
                        ShowSuccessNotification("تم الحذف", "تم حذف الجدول بنجاح");
                        await LoadSchedules();
                        await LoadDashboardData();
                    }
                    else
                    {
                        ShowErrorNotification("فشل الحذف", "حدث خطأ أثناء حذف الجدول");
                    }
                }
                catch (Exception ex)
                {
                    ShowErrorNotification("خطأ", ex.Message);
                }
            }
        }

        // Cloud operations
        private async Task UploadToCloud(string fileName)
        {
            await DialogService.OpenAsync<CloudUploadDialog>("رفع للسحابة",
                new Dictionary<string, object> { { "PreSelectedFile", fileName } },
                new DialogOptions() { Width = "600px", Height = "400px" });

            await LoadCloudUploads();
        }

        private async Task TestConnection(int configId)
        {
            try
            {
                var response = await Http.PostAsync($"api/backup/cloud/test/{configId}", null);
                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<dynamic>();
                    if (result?.Success == true)
                    {
                        ShowSuccessNotification("اختبار الاتصال", "تم الاتصال بنجاح");
                    }
                    else
                    {
                        ShowErrorNotification("فشل الاتصال", result?.Message?.ToString() ?? "فشل في الاتصال");
                    }
                }
                else
                {
                    ShowErrorNotification("خطأ في الاتصال", "حدث خطأ أثناء اختبار الاتصال");
                }
            }
            catch (Exception ex)
            {
                ShowErrorNotification("خطأ", ex.Message);
            }
        }

        private async Task ViewFiles(int configId)
        {
            try
            {
                var response = await Http.GetAsync($"api/backup/cloud/files/{configId}");
                if (response.IsSuccessStatusCode)
                {
                    var files = await response.Content.ReadFromJsonAsync<List<dynamic>>();
                    // يمكن إضافة dialog لعرض الملفات هنا
                    ShowSuccessNotification("ملفات السحابة", $"تم العثور على {files?.Count ?? 0} ملف");
                }
                else
                {
                    ShowErrorNotification("فشل جلب الملفات", "حدث خطأ أثناء جلب ملفات السحابة");
                }
            }
            catch (Exception ex)
            {
                ShowErrorNotification("خطأ", ex.Message);
            }
        }

        private async Task EditCloudConfig(CloudStorageConfigDto config)
        {
            await DialogService.OpenAsync<AppDev.Client.Pages.Administration.CloudStorage.EditCloudConfigDialog>("تعديل إعدادات التخزين السحابي",
                new Dictionary<string, object> { { "Config", config } },
                new DialogOptions() { Width = "600px", Height = "500px" });

            await LoadCloudConfigs();
        }

        private async Task DeleteCloudConfig(int configId)
        {
            var confirmed = await DialogService.Confirm("هل أنت متأكد من حذف هذا الحساب السحابي؟", "تأكيد الحذف",
                new ConfirmOptions() { OkButtonText = "نعم", CancelButtonText = "إلغاء" });

            if (confirmed == true)
            {
                try
                {
                    var response = await Http.DeleteAsync($"api/backup/cloud/configs/{configId}");
                    if (response.IsSuccessStatusCode)
                    {
                        ShowSuccessNotification("تم الحذف", "تم حذف الحساب السحابي بنجاح");
                        await LoadCloudConfigs();
                        await LoadDashboardData();
                    }
                    else
                    {
                        ShowErrorNotification("فشل الحذف", "حدث خطأ أثناء حذف الحساب السحابي");
                    }
                }
                catch (Exception ex)
                {
                    ShowErrorNotification("خطأ", ex.Message);
                }
            }
        }
    }
}
