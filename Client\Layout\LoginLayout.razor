@inherits LayoutComponentBase
@inject Microsoft.Extensions.Localization.IStringLocalizer<LoginLayout> L

<RadzenComponents />

<RadzenLayout style="background: url('images/login.jpg'); background-size: cover;">
    <RadzenBody>
        <RadzenStack JustifyContent="Radzen.JustifyContent.Center" Class="rz-mx-auto rz-p-4" Style="max-width: 1440px; height: 100%;">
            <RadzenCard class="rz-shadow-3 rz-border-radius-4" style="padding: 0; overflow: hidden; background-color: var(--rz-base-background-color);">
                <RadzenRow Gap="0">
                    <RadzenColumn Size="12" SizeSM="6" Class="rz-background-color-primary rz-p-12" style="background-image: radial-gradient(circle at 4rem 4rem, var(--rz-primary) 0%, var(--rz-primary-darker) 80%); position: relative;">
                        <RadzenImage Path="images/logo-login.png" style="width: 120px"></RadzenImage>
                        <RadzenText Text="Welcome!" TextStyle="Radzen.Blazor.TextStyle.H2" class="rz-mt-4 rz-mt-md-12 rz-pt-0 rz-pt-md-12 rz-mb-6 rz-color-on-primary rz-display-none rz-display-sm-block" />
                        <RadzenText TextStyle="Radzen.Blazor.TextStyle.Body1" class="rz-mb-12 rz-pb-0 rz-pb-md-12 rz-color-on-primary rz-display-none rz-display-sm-block">
                            Fill in your login credentials to proceed.
                        </RadzenText>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeSM="6" class="rz-p-12">
                        @Body
                    </RadzenColumn>
                </RadzenRow>
            </RadzenCard>
            <RadzenText Text="AppDev.Server v1.0.0, Copyright Ⓒ 2025" TextStyle="Radzen.Blazor.TextStyle.Caption" TextAlign="Radzen.TextAlign.Center" Style="width: 100%; margin-top: 20px" TagName="Radzen.Blazor.TagName.P"/>
        </RadzenStack>
    </RadzenBody>
</RadzenLayout>
