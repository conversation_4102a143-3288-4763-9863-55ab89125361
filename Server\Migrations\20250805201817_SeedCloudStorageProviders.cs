﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AppDev.Server.Migrations
{
    /// <inheritdoc />
    public partial class SeedCloudStorageProviders : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Insert default cloud storage providers
            migrationBuilder.InsertData(
                table: "App_CloudStorageProviders",
                columns: new[] { "ProviderName", "ProviderType", "Description", "ApiEndpoint", "IsActive", "RequiresAuthentication", "SupportedFileTypes", "MaxFileSizeBytes", "CreatedDate" },
                values: new object[,]
                {
                    {
                        "Google Drive",
                        "GoogleDrive",
                        "Google Drive cloud storage service with 15GB free storage",
                        "https://www.googleapis.com/drive/v3",
                        true,
                        true,
                        "JSON,SQL,ZIP",
                        5368709120L, // 5GB max file size
                        DateTime.UtcNow
                    },
                    {
                        "Mega.nz",
                        "Mega",
                        "Mega.nz cloud storage service with 20GB free storage and end-to-end encryption",
                        "https://g.api.mega.co.nz",
                        true,
                        true,
                        "JSON,SQL,ZIP",
                        null, // No specific limit
                        DateTime.UtcNow
                    },
                    {
                        "Microsoft OneDrive",
                        "OneDrive",
                        "Microsoft OneDrive cloud storage service with 5GB free storage",
                        "https://graph.microsoft.com/v1.0",
                        false, // Not implemented yet
                        true,
                        "JSON,SQL,ZIP",
                        107374182400L, // 100GB max file size
                        DateTime.UtcNow
                    },
                    {
                        "Dropbox",
                        "Dropbox",
                        "Dropbox cloud storage service with 2GB free storage",
                        "https://api.dropboxapi.com/2",
                        false, // Not implemented yet
                        true,
                        "JSON,SQL,ZIP",
                        2147483648L, // 2GB max file size for free accounts
                        DateTime.UtcNow
                    }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
