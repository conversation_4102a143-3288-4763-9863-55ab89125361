using AppDev.Server.Models;

namespace AppDev.Server.Services
{
    public interface ICloudStorageService
    {
        Task<CloudStorageTestResult> TestConnectionAsync(int configId);
        Task<CloudUploadResult> UploadFileAsync(CloudUploadRequest request);
        Task<bool> DeleteFileAsync(int configId, string cloudFileId);
        Task<CloudStorageQuotaInfo> GetQuotaInfoAsync(int configId);
        Task<List<CloudFileInfo>> ListFilesAsync(int configId, string? folderPath = null);
        Task<Stream> DownloadFileAsync(int configId, string cloudFileId);
        Task<bool> CreateFolderAsync(int configId, string folderPath);
        Task<string> GetShareLinkAsync(int configId, string cloudFileId);
    }

    public class CloudFileInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Path { get; set; }
        public long Size { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public bool IsFolder { get; set; }
        public string? MimeType { get; set; }
        public string? DownloadUrl { get; set; }
        public string? ShareUrl { get; set; }
    }
}
