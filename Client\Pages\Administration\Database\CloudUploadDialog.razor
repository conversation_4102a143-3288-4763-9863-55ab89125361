@using AppDev.Server.Models
@using System.Text.Json
@using Microsoft.AspNetCore.Components.Forms
@inject HttpClient Http
@inject NotificationService NotificationService
@inject DialogService DialogService



<RadzenStack Gap="1rem">
    <!-- File Selection -->
    <RadzenFieldset Text="اختيار الملف">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12">
                    <RadzenLabel Text="اختر ملف النسخة الاحتياطية" />
                    <InputFile OnChange="@OnFileSelected" accept=".zip,.sql,.json" class="form-control" />
                    <RadzenText Text="الملفات المدعومة: .zip, .sql, .json" TextStyle="TextStyle.Caption" />
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenFieldset>

    <!-- Cloud Configuration -->
    <RadzenFieldset Text="إعدادات التخزين السحابي">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="إعدادات التخزين السحابي" />
                    <RadzenDropDown @bind-Value="@selectedCloudConfigId" Data="@cloudConfigs" TextProperty="ConfigName"
                        ValueProperty="ConfigId" Placeholder="اختر إعدادات التخزين السحابي" class="w-100" />
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="نوع النسخة الاحتياطية" />
                    <RadzenDropDown @bind-Value="@backupType" Data="@backupTypes" class="w-100" />
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenFieldset>

    <!-- Upload Options -->
    <RadzenFieldset Text="خيارات الرفع">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="اسم الملف المخصص (اختياري)" />
                    <RadzenTextBox @bind-Value="@customFileName" Placeholder="اتركه فارغاً لاستخدام الاسم الأصلي"
                        class="w-100" />
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="العلامات (مفصولة بفواصل)" />
                    <RadzenTextBox @bind-Value="@tags" Placeholder="مثال: يدوي,مهم,اختبار" class="w-100" />
                </RadzenColumn>
            </RadzenRow>

            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12">
                    <RadzenLabel Text="ملاحظات (اختياري)" />
                    <RadzenTextArea @bind-Value="@notes" Placeholder="ملاحظات حول هذه النسخة الاحتياطية" class="w-100"
                        Rows="3" />
                </RadzenColumn>
            </RadzenRow>

            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenStack Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="@deleteLocalAfterUpload" Name="deleteLocal" />
                        <RadzenLabel Text="حذف الملف المحلي بعد الرفع" Component="deleteLocal" />
                    </RadzenStack>
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenFieldset>

    <!-- Upload Progress -->
    @if (isUploading)
    {
        <RadzenCard style="background: var(--rz-info-lighter); border: 1px solid var(--rz-info);">
            <RadzenStack Gap="1rem">
                <RadzenText Text="جاري الرفع..." TextStyle="TextStyle.H6" style="color: var(--rz-info-dark);" />
                <RadzenProgressBar Value="@uploadProgress" Max="100" ShowValue="true" />
                <RadzenText Text="@uploadStatus" TextStyle="TextStyle.Body2" style="color: var(--rz-info-dark);" />
            </RadzenStack>
        </RadzenCard>
    }

    <!-- Action Buttons -->
    <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" JustifyContent="JustifyContent.End" class="rz-mt-4">
        <RadzenButton Text="إلغاء" Click="@Cancel" ButtonStyle="ButtonStyle.Light" Disabled="@isUploading" />
        <RadzenButton Text="رفع للسحابة" Click="@UploadFile" ButtonStyle="ButtonStyle.Primary" IsBusy="@isUploading"
            Disabled="@(selectedFile == null || !selectedCloudConfigId.HasValue)" />
    </RadzenStack>
</RadzenStack>

@code {
    private IBrowserFile selectedFile;
    private int? selectedCloudConfigId;
    private string backupType = "Manual";
    private string customFileName = string.Empty;
    private string tags = string.Empty;
    private string notes = string.Empty;
    private bool deleteLocalAfterUpload = false;
    private List<CloudStorageConfigDto> cloudConfigs = new();
    private bool isUploading = false;
    private double uploadProgress = 0;
    private string uploadStatus = string.Empty;

    private readonly List<string> backupTypes = new() { "Manual", "JSON", "SQL", "Mixed" };

    protected override async Task OnInitializedAsync()
    {
        await LoadCloudConfigs();
    }

    private async Task LoadCloudConfigs()
    {
        try
        {
            var response = await Http.GetAsync("api/backup/cloud/configs");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                cloudConfigs = JsonSerializer.Deserialize<List<CloudStorageConfigDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = $"فشل في تحميل إعدادات التخزين السحابي: {ex.Message}",
                Duration = 4000
            });
        }
    }

    private void OnFileSelected(InputFileChangeEventArgs e)
    {
        selectedFile = e.File;
        StateHasChanged();
    }

    private async Task UploadFile()
    {
        if (selectedFile == null || !selectedCloudConfigId.HasValue)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = "تحذير",
                Detail = "يرجى اختيار ملف وإعدادات التخزين السحابي",
                Duration = 4000
            });
            return;
        }

        isUploading = true;
        uploadProgress = 0;
        uploadStatus = "جاري تحضير الملف...";
        StateHasChanged();

        try
        {
            using var content = new MultipartFormDataContent();

            // Add file
            var fileContent = new StreamContent(selectedFile.OpenReadStream(maxAllowedSize: 1024 * 1024 * 1024)); // 1GB max
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(selectedFile.ContentType);
            content.Add(fileContent, "file", selectedFile.Name);

            // Add other parameters
            content.Add(new StringContent(selectedCloudConfigId.Value.ToString()), "ConfigId");
            content.Add(new StringContent(backupType), "BackupType");
            content.Add(new StringContent(customFileName), "CustomFileName");
            content.Add(new StringContent(tags), "Tags");
            content.Add(new StringContent(notes), "Notes");
            content.Add(new StringContent(deleteLocalAfterUpload.ToString()), "DeleteLocalAfterUpload");

            uploadStatus = "جاري الرفع...";
            uploadProgress = 25;
            StateHasChanged();

            var response = await Http.PostAsync("api/backup/cloud/upload-file", content);

            uploadProgress = 75;
            uploadStatus = "جاري معالجة الاستجابة...";
            StateHasChanged();

            if (response.IsSuccessStatusCode)
            {
                uploadProgress = 100;
                uploadStatus = "تم الرفع بنجاح!";
                StateHasChanged();

                await Task.Delay(1000); // Show success message briefly

                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Success,
                    Summary = "نجح",
                    Detail = "تم رفع الملف للتخزين السحابي بنجاح",
                    Duration = 4000
                });

                DialogService.Close(true);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent);

                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Error,
                    Summary = "خطأ",
                    Detail = errorResult.GetProperty("Message").GetString() ?? "فشل في رفع الملف",
                    Duration = 4000
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = $"حدث خطأ: {ex.Message}",
                Duration = 4000
            });
        }
        finally
        {
            isUploading = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        DialogService.Close(false);
    }

    // Data models
    public class CloudStorageConfigDto
    {
        public int ConfigId { get; set; }
        public string ConfigName { get; set; } = string.Empty;
        public string ProviderName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }
}
