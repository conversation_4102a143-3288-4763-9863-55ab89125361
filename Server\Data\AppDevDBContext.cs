using System;
using System.Linq;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using AppDev.Server.Models.AppDevDB;

namespace AppDev.Server.Data
{
    public partial class AppDevDBContext : DbContext
    {
        public AppDevDBContext()
        {
        }

        public AppDevDBContext(DbContextOptions<AppDevDBContext> options) : base(options)
        {
        }

        partial void OnModelBuilding(ModelBuilder builder);

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            builder.Entity<AppReportDesign>()
                .HasOne(i => i.Report)
                .WithMany(i => i.AppReportDesigns)
                .HasForeignKey(i => i.ReportId)
                .HasPrincipalKey(i => i.ReportId);
            builder.Entity<AppReportPermission>()
                .HasOne(i => i.Report)
                .WithMany(i => i.AppReportPermissions)
                .HasForeignKey(i => i.ReportId)
                .HasPrincipalKey(i => i.ReportId);
            builder.Entity<AppReportDesign>()
                .HasOne(i => i.PrintSetting)
                .WithMany(i => i.AppReportDesigns)
                .HasForeignKey(i => i.PrintSettingId)
                .HasPrincipalKey(i => i.PrintSettingId);

            // Cloud Storage Relationships
            builder.Entity<AppCloudStorageConfig>()
                .HasOne(i => i.Provider)
                .WithMany(i => i.CloudStorageConfigs)
                .HasForeignKey(i => i.ProviderId)
                .HasPrincipalKey(i => i.ProviderId);

            builder.Entity<AppBackupCloudUpload>()
                .HasOne(i => i.Config)
                .WithMany(i => i.BackupCloudUploads)
                .HasForeignKey(i => i.ConfigId)
                .HasPrincipalKey(i => i.ConfigId);

            builder.Entity<AppBackupCloudUpload>()
                .HasOne(i => i.Provider)
                .WithMany(i => i.BackupCloudUploads)
                .HasForeignKey(i => i.ProviderId)
                .HasPrincipalKey(i => i.ProviderId);

            builder.Entity<AppBackupSchedule>()
                .HasOne(i => i.CloudConfig)
                .WithMany(i => i.BackupSchedules)
                .HasForeignKey(i => i.CloudConfigId)
                .HasPrincipalKey(i => i.ConfigId);

            OnModelBuilding(builder);
        }

        public DbSet<AppReport> AppReports { get; set; }
        public DbSet<AppReportDesign> AppReportDesigns { get; set; }
        public DbSet<AppReportPermission> AppReportPermissions { get; set; }
        public DbSet<AppReportPrintSetting> AppReportPrintSettings { get; set; }

        // Error Logging Tables
        public DbSet<AppErrorLog> AppErrorLogs { get; set; }

        // Cloud Storage Tables
        public DbSet<AppCloudStorageProvider> AppCloudStorageProviders { get; set; }
        public DbSet<AppCloudStorageConfig> AppCloudStorageConfigs { get; set; }
        public DbSet<AppBackupCloudUpload> AppBackupCloudUploads { get; set; }
        public DbSet<AppBackupSchedule> AppBackupSchedules { get; set; }

        protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
        {
            configurationBuilder.Conventions.Add(_ => new BlankTriggerAddingConvention());
        }
    }
}