using Radzen;
using AppDev.Server.Components;
using Microsoft.EntityFrameworkCore;
using Microsoft.OData.ModelBuilder;
using Microsoft.AspNetCore.OData;
using AppDev.Server.Data;
using Microsoft.AspNetCore.Identity;
using AppDev.Server.Models;
using Microsoft.AspNetCore.Components.Authorization;

var builder = WebApplication.CreateBuilder(args);
// Add services to the container.
builder.Services.AddRazorComponents().AddInteractiveServerComponents().AddHubOptions(options => options.MaximumReceiveMessageSize = 10 * 1024 * 1024).AddInteractiveWebAssemblyComponents();
builder.Services.AddControllers();
builder.Services.AddRadzenComponents();
builder.Services.AddRadzenCookieThemeService(options =>
{
    options.Name = "AppDevTheme";
    options.Duration = TimeSpan.FromDays(365);
});
builder.Services.AddHttpClient();
builder.Services.AddScoped<AppDev.Server.AppDevDBService>();
builder.Services.AddHostedService<AppDev.Server.Services.BackupSchedulerService>();
// Register Cloud Storage Services
builder.Services.AddScoped<AppDev.Server.Services.GoogleDriveService>();
builder.Services.AddScoped<AppDev.Server.Services.MegaService>();
builder.Services.AddScoped<AppDev.Server.Services.ICloudStorageManager, AppDev.Server.Services.CloudStorageManager>();
// Register Error Logging Services
builder.Services.AddScoped<AppDev.Server.Services.IErrorLoggingService, AppDev.Server.Services.ErrorLoggingService>();
builder.Services.AddHostedService<AppDev.Server.Services.ErrorLogCleanupService>();
// Register Stimulsoft Services - Temporarily disabled
// builder.Services.AddScoped<AppDev.Server.Services.IStimulsoftReportService, AppDev.Server.Services.StimulsoftReportService>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddDbContext<AppDev.Server.Data.AppDevDBContext>(options =>
{
    options.UseMySql(builder.Configuration.GetConnectionString("AppDevDBConnection"), ServerVersion.AutoDetect(builder.Configuration.GetConnectionString("AppDevDBConnection")));
});
builder.Services.AddControllers().AddOData(opt =>
{
    var oDataBuilderAppDevDB = new ODataConventionModelBuilder();
    opt.AddRouteComponents("odata/AppDevDB", oDataBuilderAppDevDB.GetEdmModel()).Count().Filter().OrderBy().Expand().Select().SetMaxTop(null).TimeZone = TimeZoneInfo.Utc;
});
builder.Services.AddScoped<AppDev.Client.AppDevDBService>();
builder.Services.AddHttpClient("AppDev.Server").ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler { UseCookies = false }).AddHeaderPropagation(o => o.Headers.Add("Cookie"));
builder.Services.AddHeaderPropagation(o => o.Headers.Add("Cookie"));
builder.Services.AddAuthentication();
builder.Services.AddAuthorization();
builder.Services.AddScoped<AppDev.Client.SecurityService>();
builder.Services.AddDbContext<ApplicationIdentityDbContext>(options =>
{
    options.UseMySql(builder.Configuration.GetConnectionString("AppDevDBConnection"), ServerVersion.AutoDetect(builder.Configuration.GetConnectionString("AppDevDBConnection")));
});
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>().AddEntityFrameworkStores<ApplicationIdentityDbContext>().AddDefaultTokenProviders();
builder.Services.AddControllers().AddOData(o =>
{
    var oDataBuilder = new ODataConventionModelBuilder();
    oDataBuilder.EntitySet<ApplicationUser>("ApplicationUsers");
    var usersType = oDataBuilder.StructuralTypes.First(x => x.ClrType == typeof(ApplicationUser));
    usersType.AddProperty(typeof(ApplicationUser).GetProperty(nameof(ApplicationUser.Password)));
    usersType.AddProperty(typeof(ApplicationUser).GetProperty(nameof(ApplicationUser.ConfirmPassword)));
    oDataBuilder.EntitySet<ApplicationRole>("ApplicationRoles");
    o.AddRouteComponents("odata/Identity", oDataBuilder.GetEdmModel()).Count().Filter().OrderBy().Expand().Select().SetMaxTop(null).TimeZone = TimeZoneInfo.Utc;
});
builder.Services.AddScoped<AuthenticationStateProvider, AppDev.Client.ApplicationAuthenticationStateProvider>();
builder.Services.AddLocalization();
builder.Services.AddSingleton<IWebHostEnvironment>(builder.Environment);
var app = builder.Build();
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
// Add Global Error Handling Middleware
app.UseMiddleware<AppDev.Server.Middleware.GlobalErrorHandlingMiddleware>();
app.MapControllers();
app.UseHeaderPropagation();
app.UseRequestLocalization(options => options.AddSupportedCultures("en", "ar").AddSupportedUICultures("en", "ar").SetDefaultCulture("en"));
app.UseStaticFiles();
app.UseAuthentication();
app.UseAuthorization();
app.UseAntiforgery();
app.MapRazorComponents<App>().AddInteractiveServerRenderMode().AddInteractiveWebAssemblyRenderMode().AddAdditionalAssemblies(typeof(AppDev.Client._Imports).Assembly);
app.Services.CreateScope().ServiceProvider.GetRequiredService<ApplicationIdentityDbContext>().Database.Migrate();
app.Run();