#nullable enable
using AppDev.Server.Data;
using AppDev.Server.Models;
using AppDev.Server.Models.AppDevDB;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace AppDev.Server.Services
{
    public class BackupSchedulerService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<BackupSchedulerService> _logger;
        private readonly IConfiguration _configuration;
        private Timer? _timer;

        public BackupSchedulerService(
            IServiceProvider serviceProvider,
            ILogger<BackupSchedulerService> logger,
            IConfiguration configuration)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("خدمة النسخ الاحتياطي المجدول بدأت");

            // قراءة إعدادات الجدولة من التكوين
            var intervalHours = _configuration.GetValue<int>("BackupScheduler:IntervalHours", 24);
            var enableAutoBackup = _configuration.GetValue<bool>("BackupScheduler:Enabled", false);

            if (!enableAutoBackup)
            {
                _logger.LogInformation("النسخ الاحتياطي التلقائي معطل");
                return;
            }

            var interval = TimeSpan.FromHours(intervalHours);
            _timer = new Timer(DoBackup, null, TimeSpan.Zero, interval);

            // انتظار حتى يتم إيقاف الخدمة
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }

        private async void DoBackup(object? state)
        {
            try
            {
                _logger.LogInformation("بدء النسخ الاحتياطي المجدول");

                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDevDBContext>();
                var webHostEnvironment = scope.ServiceProvider.GetRequiredService<IWebHostEnvironment>();
                var cloudStorageManager = scope.ServiceProvider.GetRequiredService<ICloudStorageManager>();

                var backupFolderPath = Path.Combine(webHostEnvironment.ContentRootPath, "Backups");
                if (!Directory.Exists(backupFolderPath))
                {
                    Directory.CreateDirectory(backupFolderPath);
                }

                // إنشاء نسخة احتياطية JSON
                var result = await CreateScheduledBackup(dbContext, backupFolderPath);

                if (result.Success)
                {
                    _logger.LogInformation($"تم إنشاء النسخة الاحتياطية المجدولة بنجاح: {result.BackupPath}");

                    // رفع للسحابة إذا كان مفعل
                    await UploadToCloudIfEnabled(dbContext, cloudStorageManager, result.BackupPath);

                    // تنظيف النسخ القديمة
                    await CleanupOldBackups(backupFolderPath);
                }
                else
                {
                    _logger.LogError($"فشل في إنشاء النسخة الاحتياطية المجدولة: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في النسخ الاحتياطي المجدول");
            }
        }

        private async Task<BackupResult> CreateScheduledBackup(AppDevDBContext dbContext, string backupFolderPath)
        {
            try
            {
                var backupFileName = $"AppDevDB_scheduled_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.json";
                var backupFilePath = Path.Combine(backupFolderPath, backupFileName);

                var dataToBackup = new Dictionary<string, object>();

                var tableNames = dbContext.Model.GetEntityTypes()
                    .Select(t => t.GetTableName())
                    .Where(t => t != null)
                    .Distinct()
                    .ToList();

                foreach (var tableName in tableNames)
                {
                    if (tableName != null)
                    {
                        var entityType = dbContext.Model.GetEntityTypes().FirstOrDefault(e => e.GetTableName() == tableName);
                        if (entityType != null)
                        {
                            var data = await GetTableData(dbContext, entityType.ClrType);
                            dataToBackup[tableName] = data;
                        }
                    }
                }

                var json = JsonSerializer.Serialize(dataToBackup, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(backupFilePath, json);

                // ضغط وتشفير الملف
                var zipFileName = $"AppDevDB_scheduled_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.zip";
                var zipFilePath = Path.Combine(backupFolderPath, zipFileName);

                using (var archive = System.IO.Compression.ZipFile.Open(zipFilePath, System.IO.Compression.ZipArchiveMode.Create))
                {
                    var entry = archive.CreateEntry(Path.GetFileName(backupFileName), System.IO.Compression.CompressionLevel.Optimal);
                    using (var entryStream = entry.Open())
                    using (var fileStream = File.OpenRead(backupFilePath))
                    {
                        await fileStream.CopyToAsync(entryStream);
                    }
                }

                // حذف الملف المؤقت
                File.Delete(backupFilePath);

                return new BackupResult { Success = true, BackupPath = zipFilePath, Message = "تم إنشاء النسخة الاحتياطية المجدولة بنجاح" };
            }
            catch (Exception ex)
            {
                return new BackupResult { Success = false, Message = $"خطأ في النسخ الاحتياطي المجدول: {ex.Message}" };
            }
        }

        private async Task<List<object>> GetTableData(AppDevDBContext dbContext, Type entityType)
        {
            var method = typeof(BackupSchedulerService).GetMethod("GetTableDataGeneric", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.MakeGenericMethod(entityType);
            if (method != null)
            {
                var task = (Task<List<object>>?)method.Invoke(this, new object[] { dbContext });
                if (task != null)
                    return await task;
            }
            return new List<object>();
        }

        private async Task<List<object>> GetTableDataGeneric<T>(AppDevDBContext dbContext) where T : class
        {
            var data = await dbContext.Set<T>().ToListAsync();
            return data.Cast<object>().ToList();
        }

        private async Task UploadToCloudIfEnabled(AppDevDBContext dbContext, ICloudStorageManager cloudStorageManager, string? backupFilePath)
        {
            try
            {
                if (string.IsNullOrEmpty(backupFilePath) || !File.Exists(backupFilePath))
                {
                    _logger.LogWarning("مسار النسخة الاحتياطية غير صحيح أو الملف غير موجود");
                    return;
                }

                // البحث عن إعدادات النسخ الاحتياطي المجدول مع الرفع السحابي المفعل
                var activeSchedules = await dbContext.AppBackupSchedules
                    .Where(s => s.IsActive && s.UploadToCloud && s.CloudConfigId.HasValue)
                    .Include(s => s.CloudConfig)
                    .ToListAsync();

                if (!activeSchedules.Any())
                {
                    _logger.LogInformation("لا توجد جداول نسخ احتياطي مفعلة مع الرفع السحابي");
                    return;
                }

                // استخدام أول إعدادات نشطة للرفع السحابي
                var schedule = activeSchedules.First();

                if (schedule.CloudConfig == null || !schedule.CloudConfig.IsActive)
                {
                    _logger.LogWarning($"إعدادات التخزين السحابي غير نشطة للجدول: {schedule.ScheduleName}");
                    return;
                }

                _logger.LogInformation($"بدء رفع النسخة الاحتياطية للسحابة باستخدام: {schedule.CloudConfig.ConfigName}");

                var uploadRequest = new CloudUploadRequest
                {
                    ConfigId = schedule.CloudConfigId!.Value,
                    LocalFilePath = backupFilePath,
                    BackupType = "Scheduled",
                    CustomFileName = null,
                    Tags = "scheduled,automatic",
                    Notes = $"نسخة احتياطية مجدولة - {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    DeleteLocalAfterUpload = schedule.DeleteLocalAfterUpload
                };

                var uploadResult = await cloudStorageManager.UploadFileAsync(uploadRequest);

                if (uploadResult.Success)
                {
                    _logger.LogInformation($"تم رفع النسخة الاحتياطية للسحابة بنجاح: {uploadResult.CloudFileId}");

                    // تحديث إحصائيات الجدول
                    schedule.LastRunTime = DateTime.UtcNow;
                    schedule.LastRunStatus = "Success";
                    schedule.LastRunMessage = "تم إنشاء النسخة الاحتياطية ورفعها للسحابة بنجاح";
                    schedule.SuccessfulRuns++;

                    await dbContext.SaveChangesAsync();
                }
                else
                {
                    _logger.LogError($"فشل في رفع النسخة الاحتياطية للسحابة: {uploadResult.Message}");

                    // تحديث إحصائيات الجدول
                    schedule.LastRunTime = DateTime.UtcNow;
                    schedule.LastRunStatus = "Partial";
                    schedule.LastRunMessage = $"تم إنشاء النسخة الاحتياطية لكن فشل الرفع للسحابة: {uploadResult.Message}";
                    schedule.FailedRuns++;

                    await dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في رفع النسخة الاحتياطية للسحابة");
            }
        }

        private async Task CleanupOldBackups(string backupFolderPath)
        {
            try
            {
                var maxBackupsToKeep = _configuration.GetValue<int>("BackupScheduler:MaxBackupsToKeep", 10);

                var backupFiles = Directory.GetFiles(backupFolderPath, "*scheduled*.zip")
                    .Select(f => new FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .ToList();

                if (backupFiles.Count > maxBackupsToKeep)
                {
                    var filesToDelete = backupFiles.Skip(maxBackupsToKeep);
                    foreach (var file in filesToDelete)
                    {
                        try
                        {
                            file.Delete();
                            _logger.LogInformation($"تم حذف النسخة الاحتياطية القديمة: {file.Name}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"فشل في حذف النسخة الاحتياطية القديمة: {file.Name}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنظيف النسخ الاحتياطية القديمة");
            }
        }

        public override void Dispose()
        {
            _timer?.Dispose();
            base.Dispose();
        }
    }

    public class BackupScheduleSettings
    {
        public bool Enabled { get; set; } = false;
        public int IntervalHours { get; set; } = 24;
        public int MaxBackupsToKeep { get; set; } = 10;
        public string BackupType { get; set; } = "JSON";
        public bool EnableEncryption { get; set; } = true;
    }
}
