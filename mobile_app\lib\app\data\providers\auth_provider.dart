import 'package:get/get.dart';
import 'package:dio/dio.dart' as dio_pkg;
import 'dart:io'; // Required for Platform check and HttpClient
import 'package:dio/io.dart'; // Import for IOHttpClientAdapter

class AuthProvider extends GetxService {
  final String baseUrl = 'https://localhost:5001'; // Replace with your API base URL
  late dio_pkg.Dio _dio;

  AuthProvider() {
    _dio = dio_pkg.Dio(dio_pkg.BaseOptions(baseUrl: baseUrl));

    // For development only! Do NOT use in production.
    // It bypasses SSL certificate validation for localhost.
    if (Platform.isAndroid || Platform.isIOS || Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      _dio.httpClientAdapter = IOHttpClientAdapter(
        createHttpClient: () {
          final client = HttpClient();
          client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
          return client;
        },
      );
    }
  }

  Future<dio_pkg.Response> login(String email, String password) async {
    try {
      final response = await _dio.post(
        '/Account/Login',
        data: {'email': email, 'password': password},
      );
      return response;
    } on dio_pkg.DioException catch (e) {
      return dio_pkg.Response(requestOptions: e.requestOptions, statusCode: e.response?.statusCode, data: e.response?.data);
    }
  }

  // You can add other API calls here (e.g., logout, refresh token)
}