@page "/admin/database/maintenance"
@attribute [Authorize]
@using AppDev.Server.Models
@using Microsoft.AspNetCore.Components.Web
@attribute [StreamRendering]

<PageTitle>صيانة قاعدة البيانات</PageTitle>

<style>
    .stat-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .stat-card:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2) !important;
    }
</style>

<RadzenStack Gap="1rem">
    <!-- Header -->
    <RadzenRow AlignItems="AlignItems.Center" style="margin-bottom: 2rem;">
        <RadzenColumn Size="12" SizeMD="8">
            <RadzenStack Gap="0.5rem">
                <RadzenText Text="صيانة قاعدة البيانات" TextStyle="TextStyle.DisplayH4" TagName="TagName.H1"
                    style="margin: 0; color: var(--rz-primary);" />
                <RadzenText Text="إدارة وصيانة قاعدة البيانات والتأكد من سلامتها" TextStyle="TextStyle.Body1"
                    style="color: var(--rz-text-secondary-color);" />
            </RadzenStack>
        </RadzenColumn>
        <RadzenColumn Size="12" SizeMD="4">
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center"
                JustifyContent="JustifyContent.End" Gap="0.5rem">
                <RadzenButton Icon="refresh" Text="فحص شامل" Click="@CheckDatabaseHealth" Variant="Variant.Filled"
                    ButtonStyle="ButtonStyle.Primary" Size="ButtonSize.Medium" />
            </RadzenStack>
        </RadzenColumn>
    </RadzenRow>

    <!-- Health Status Card -->
    <RadzenCard style="margin-bottom: 1.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border: 1px solid #dee2e6; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border-radius: 12px;">
        <RadzenStack Gap="1.5rem">
            <RadzenRow AlignItems="AlignItems.Center" JustifyContent="JustifyContent.SpaceBetween">
                <RadzenColumn Size="12" SizeMD="8">
                    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.75rem">
                        <RadzenIcon Icon="dashboard" style="font-size: 1.8rem; color: var(--rz-primary);" />
                        <RadzenText Text="حالة قاعدة البيانات" TextStyle="TextStyle.H4"
                                  style="margin: 0; color: var(--rz-primary); font-weight: 700;" />
                    </RadzenStack>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="4" style="text-align: right;">
                    @if (healthReport != null)
                    {
                        <RadzenBadge BadgeStyle="@GetHealthBadgeStyle(healthReport.OverallHealth)"
                            Text="@healthReport.OverallHealth"
                            style="font-size: 1.1rem; padding: 0.7rem 1.4rem; border-radius: 25px; font-weight: 600; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);" />
                    }
                </RadzenColumn>
            </RadzenRow>

            @if (healthReport != null)
            {
                <RadzenRow Gap="1rem">
                    <RadzenColumn Size="6" SizeMD="4">
                        <RadzenCard class="stat-card"
                            style="text-align: center; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; border: none; height: 120px; border-radius: 12px; box-shadow: 0 6px 12px rgba(79, 172, 254, 0.3);">
                            <RadzenStack Gap="0.5rem" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" style="height: 100%;">
                                <RadzenIcon Icon="@(healthReport.ConnectionStatus ? "wifi" : "wifi_off")"
                                    style="font-size: 1.8rem; opacity: 0.9;" />
                                <RadzenText Text="@(healthReport.ConnectionStatus ? "متصل" : "منقطع")"
                                    TextStyle="TextStyle.H5" style="margin: 0; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.2);" />
                                <RadzenText Text="حالة الاتصال" TextStyle="TextStyle.Body2"
                                    style="margin: 0; opacity: 0.95; font-weight: 500;" />
                            </RadzenStack>
                        </RadzenCard>
                    </RadzenColumn>
                    <RadzenColumn Size="6" SizeMD="4">
                        <RadzenCard class="stat-card"
                            style="text-align: center; background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; border: none; height: 120px; border-radius: 12px; box-shadow: 0 6px 12px rgba(250, 112, 154, 0.3);">
                            <RadzenStack Gap="0.5rem" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" style="height: 100%;">
                                <RadzenIcon Icon="backup" style="font-size: 1.8rem; opacity: 0.9;" />
                                <RadzenText
                                    Text="@healthReport.Statistics.LastBackupFormatted"
                                    TextStyle="TextStyle.H6" style="margin: 0; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.2); line-height: 1.2;" />
                                <RadzenText Text="آخر نسخة احتياطية" TextStyle="TextStyle.Body2"
                                    style="margin: 0; opacity: 0.95; font-weight: 500;" />
                            </RadzenStack>
                        </RadzenCard>
                    </RadzenColumn>
                    <RadzenColumn Size="6" SizeMD="4">
                        <RadzenCard class="stat-card"
                            style="text-align: center; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #2d3748; border: none; height: 120px; border-radius: 12px; box-shadow: 0 6px 12px rgba(168, 237, 234, 0.3);">
                            <RadzenStack Gap="0.5rem" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" style="height: 100%;">
                                <RadzenIcon Icon="folder_zip" style="font-size: 1.8rem; opacity: 0.8;" />
                                <RadzenText Text="@healthReport.Statistics.BackupCount.ToString()" TextStyle="TextStyle.H3"
                                    style="margin: 0; font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.1);" />
                                <RadzenText Text="النسخ الاحتياطية" TextStyle="TextStyle.Body2"
                                    style="margin: 0; opacity: 0.9; font-weight: 500;" />
                            </RadzenStack>
                        </RadzenCard>
                    </RadzenColumn>
                </RadzenRow>
            }
        </RadzenStack>
    </RadzenCard>

    <!-- Action Buttons -->
    <RadzenCard style="background: var(--rz-base-50);">
        <RadzenStack Gap="1rem">
            <RadzenText Text="أدوات الصيانة" TextStyle="TextStyle.H5" style="margin: 0; color: var(--rz-primary);" />
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="3">
                    <RadzenButton Icon="build" Text="تطبيق Migrations" Click="@ApplyMigrations" Variant="Variant.Filled"
                        ButtonStyle="ButtonStyle.Success" Size="ButtonSize.Large" style="width: 100%; height: 60px;" />
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="3">
                    <RadzenButton Icon="tune" Text="تحسين قاعدة البيانات" Click="@OptimizeDatabase" Variant="Variant.Filled"
                        ButtonStyle="ButtonStyle.Info" Size="ButtonSize.Large" style="width: 100%; height: 60px;" />
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="3">
                    <RadzenButton Icon="build_circle" Text="إصلاح قاعدة البيانات" Click="@RepairDatabase" Variant="Variant.Filled"
                        ButtonStyle="ButtonStyle.Warning" Size="ButtonSize.Large" style="width: 100%; height: 60px;" />
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="3">
                    <RadzenButton Icon="backup" Text="النسخ الاحتياطي" Click="@NavigateToBackup" Variant="Variant.Filled"
                        ButtonStyle="ButtonStyle.Primary" Size="ButtonSize.Large" style="width: 100%; height: 60px;" />
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenCard>



    <!-- Tables Status -->
    @if (healthReport?.RequiredTables?.Any() == true)
    {
        <RadzenCard>
            <RadzenText Text="حالة الجداول المطلوبة" TextStyle="TextStyle.H5" style="margin-bottom: 1rem;" />
            <RadzenDataGrid Data="@healthReport.RequiredTables" TItem="TableStatus" AllowSorting="true">
                <Columns>
                    <RadzenDataGridColumn TItem="TableStatus" Property="Name" Title="اسم الجدول" />
                    <RadzenDataGridColumn TItem="TableStatus" Property="Status" Title="الحالة">
                        <Template Context="table">
                            <RadzenBadge BadgeStyle="@(table.Exists? BadgeStyle.Success: BadgeStyle.Danger)"
                                Text="@table.Status" />
                        </Template>
                    </RadzenDataGridColumn>
                    <RadzenDataGridColumn TItem="TableStatus" Property="RecordCount" Title="عدد السجلات" />
                </Columns>
            </RadzenDataGrid>
        </RadzenCard>
    }

    <!-- Activity Log -->
    @if (activityLog.Any())
    {
        <RadzenCard>
            <RadzenText Text="سجل الأنشطة" TextStyle="TextStyle.H5" style="margin-bottom: 1rem;" />
            <RadzenStack Gap="0.5rem">
                @foreach (var log in activityLog.TakeLast(10))
                {
                    <RadzenAlert AlertStyle="@GetLogAlertStyle(log.Type)" Size="AlertSize.Small" Variant="Variant.Flat">
                        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center"
                            JustifyContent="JustifyContent.SpaceBetween">
                            <RadzenText Text="@log.Message" />
                            <RadzenText Text="@log.Timestamp.ToString("HH:mm:ss")" TextStyle="TextStyle.Caption" />
                        </RadzenStack>
                    </RadzenAlert>
                }
            </RadzenStack>
        </RadzenCard>
    }

    <!-- Loading Indicator -->
    @if (isLoading)
    {
        <RadzenCard
            style="text-align: center; padding: 3rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
            <RadzenStack Gap="1rem" AlignItems="AlignItems.Center">
                <RadzenProgressBarCircular ShowValue="false" Mode="ProgressBarMode.Indeterminate"
                    style="--rz-progressbar-color: white;" Size="ProgressBarCircularSize.Large" />
                <RadzenText Text="جاري المعالجة..." TextStyle="TextStyle.H6" style="margin: 0; color: white;" />
                <RadzenText Text="يرجى الانتظار..." TextStyle="TextStyle.Body2" style="margin: 0; opacity: 0.8;" />
            </RadzenStack>
        </RadzenCard>
    }
</RadzenStack>
