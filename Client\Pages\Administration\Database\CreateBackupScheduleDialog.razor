
@using AppDev.Server.Models
@using System.Text.Json
@inject HttpClient Http
@inject NotificationService NotificationService
@inject DialogService DialogService



<RadzenStack Gap="1rem">
    <!-- Basic Information -->
    <RadzenFieldset Text="المعلومات الأساسية">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="اسم الجدول" />
                    <RadzenTextBox @bind-Value="@request.ScheduleName" Placeholder="اسم الجدول" class="w-100" />
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="الوصف (اختياري)" />
                    <RadzenTextBox @bind-Value="@request.Description" Placeholder="وصف الجدول" class="w-100" />
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenFieldset>

    <!-- Backup Settings -->
    <RadzenFieldset Text="إعدادات النسخ الاحتياطي">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="نوع النسخة الاحتياطية" />
                    <RadzenDropDown @bind-Value="@request.BackupType" Data="@backupTypes" class="w-100" />
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="نوع الجدولة" />
                    <RadzenDropDown @bind-Value="@request.ScheduleType" Data="@scheduleTypes" class="w-100" />
                </RadzenColumn>
            </RadzenRow>

            @if (request.ScheduleType == "Cron")
            {
                <RadzenRow Gap="1rem">
                    <RadzenColumn Size="12">
                        <RadzenLabel Text="تعبير Cron" />
                        <RadzenTextBox @bind-Value="@request.CronExpression" Placeholder="0 2 * * *" class="w-100" />
                        <RadzenText Text="مثال: 0 2 * * * (كل يوم في الساعة 2:00 صباحاً)" TextStyle="TextStyle.Caption" />
                    </RadzenColumn>
                </RadzenRow>
            }
        </RadzenStack>
    </RadzenFieldset>

    <!-- Cloud Storage Settings -->
    <RadzenFieldset Text="إعدادات التخزين السحابي">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenStack Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="@request.UploadToCloud" Name="uploadToCloud" />
                        <RadzenLabel Text="رفع للتخزين السحابي" Component="uploadToCloud" />
                    </RadzenStack>
                </RadzenColumn>
                @if (request.UploadToCloud)
                {
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenLabel Text="إعدادات التخزين السحابي" />
                        <RadzenDropDown @bind-Value="@request.CloudConfigId" Data="@cloudConfigs" TextProperty="ConfigName"
                            ValueProperty="ConfigId" Placeholder="اختر إعدادات التخزين السحابي" class="w-100" />
                    </RadzenColumn>
                }
            </RadzenRow>

            @if (request.UploadToCloud)
            {
                <RadzenRow Gap="1rem">
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenStack Gap="0.5rem">
                            <RadzenCheckBox @bind-Value="@request.DeleteLocalAfterUpload" Name="deleteLocal" />
                            <RadzenLabel Text="حذف الملف المحلي بعد الرفع" Component="deleteLocal" />
                        </RadzenStack>
                    </RadzenColumn>
                    <RadzenColumn Size="12" SizeMD="6">
                        <RadzenLabel Text="مدة الاحتفاظ في السحابة (بالأيام)" />
                        <RadzenNumeric @bind-Value="@request.CloudRetentionDays" Min="1" Max="365" class="w-100" />
                    </RadzenColumn>
                </RadzenRow>
            }
        </RadzenStack>
    </RadzenFieldset>

    <!-- Local Storage Settings -->
    <RadzenFieldset Text="إعدادات التخزين المحلي">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="مدة الاحتفاظ المحلي (بالأيام)" />
                    <RadzenNumeric @bind-Value="@request.LocalRetentionDays" Min="1" Max="365" class="w-100" />
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenLabel Text="الحد الأقصى للنسخ المحلية" />
                    <RadzenNumeric @bind-Value="@request.MaxLocalBackups" Min="1" Max="100" class="w-100" />
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenFieldset>

    <!-- Advanced Settings -->
    <RadzenFieldset Text="الإعدادات المتقدمة">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="4">
                    <RadzenStack Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="@request.CompressBackups" Name="compress" />
                        <RadzenLabel Text="ضغط النسخ الاحتياطية" Component="compress" />
                    </RadzenStack>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="4">
                    <RadzenStack Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="@request.EncryptBackups" Name="encrypt" />
                        <RadzenLabel Text="تشفير النسخ الاحتياطية" Component="encrypt" />
                    </RadzenStack>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="4">
                    <RadzenStack Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="@request.VerifyIntegrity" Name="verify" />
                        <RadzenLabel Text="التحقق من سلامة البيانات" Component="verify" />
                    </RadzenStack>
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenFieldset>

    <!-- Notification Settings -->
    <RadzenFieldset Text="إعدادات الإشعارات">
        <RadzenStack Gap="1rem">
            <RadzenRow Gap="1rem">
                <RadzenColumn Size="12" SizeMD="4">
                    <RadzenStack Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="@request.NotifyOnSuccess" Name="notifySuccess" />
                        <RadzenLabel Text="إشعار عند النجاح" Component="notifySuccess" />
                    </RadzenStack>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="4">
                    <RadzenStack Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="@request.NotifyOnFailure" Name="notifyFailure" />
                        <RadzenLabel Text="إشعار عند الفشل" Component="notifyFailure" />
                    </RadzenStack>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="4">
                    <RadzenLabel Text="البريد الإلكتروني (اختياري)" />
                    <RadzenTextBox @bind-Value="@request.NotificationEmail" Placeholder="<EMAIL>"
                        class="w-100" />
                </RadzenColumn>
            </RadzenRow>
        </RadzenStack>
    </RadzenFieldset>

    <!-- Action Buttons -->
    <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" JustifyContent="JustifyContent.End" class="rz-mt-4">
        <RadzenButton Text="إلغاء" Click="@Cancel" ButtonStyle="ButtonStyle.Light" />
        <RadzenButton Text="حفظ" Click="@Save" ButtonStyle="ButtonStyle.Primary" IsBusy="@isSaving" />
    </RadzenStack>
</RadzenStack>

@code {
    private CreateBackupScheduleRequest request = new();
    private List<CloudStorageConfigDto>? cloudConfigs;
    private bool isSaving = false;

    private readonly List<string> backupTypes = new() { "JSON", "SQL", "Both" };
    private readonly List<string> scheduleTypes = new() { "Daily", "Weekly", "Monthly", "Cron" };

    protected override async Task OnInitializedAsync()
    {
        // Set default values
        request.BackupType = "JSON";
        request.ScheduleType = "Daily";
        request.LocalRetentionDays = 30;
        request.CloudRetentionDays = 90;
        request.MaxLocalBackups = 10;
        request.MaxCloudBackups = 30;
        request.CompressBackups = true;
        request.NotifyOnSuccess = true;
        request.NotifyOnFailure = true;

        await LoadCloudConfigs();
    }

    private async Task LoadCloudConfigs()
    {
        try
        {
            var response = await Http.GetAsync("api/backup/cloud/configs");
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                cloudConfigs = JsonSerializer.Deserialize<List<CloudStorageConfigDto>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = $"فشل في تحميل إعدادات التخزين السحابي: {ex.Message}",
                Duration = 4000
            });
        }
    }

    private async Task Save()
    {
        if (string.IsNullOrWhiteSpace(request.ScheduleName))
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = "تحذير",
                Detail = "يرجى إدخال اسم الجدول",
                Duration = 4000
            });
            return;
        }

        if (request.ScheduleType == "Cron" && string.IsNullOrWhiteSpace(request.CronExpression))
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = "تحذير",
                Detail = "يرجى إدخال تعبير Cron",
                Duration = 4000
            });
            return;
        }

        if (request.UploadToCloud && !request.CloudConfigId.HasValue)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = "تحذير",
                Detail = "يرجى اختيار إعدادات التخزين السحابي",
                Duration = 4000
            });
            return;
        }

        isSaving = true;
        StateHasChanged();

        try
        {
            var response = await Http.PostAsJsonAsync("api/backup/schedules", request);
            if (response.IsSuccessStatusCode)
            {
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Success,
                    Summary = "نجح",
                    Detail = "تم إنشاء جدول النسخ الاحتياطي بنجاح",
                    Duration = 4000
                });

                DialogService.Close(true);
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorResult = JsonSerializer.Deserialize<JsonElement>(errorContent);

                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Error,
                    Summary = "خطأ",
                    Detail = errorResult.GetProperty("Message").GetString() ?? "فشل في إنشاء الجدول",
                    Duration = 4000
                });
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Error,
                Summary = "خطأ",
                Detail = $"حدث خطأ: {ex.Message}",
                Duration = 4000
            });
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private void Cancel()
    {
        DialogService.Close(false);
    }

    // Data models
    public class CreateBackupScheduleRequest
    {
        public string ScheduleName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string BackupType { get; set; } = "JSON";
        public string ScheduleType { get; set; } = "Daily";
        public string CronExpression { get; set; } = "0 2 * * *";
        public bool UploadToCloud { get; set; } = false;
        public int? CloudConfigId { get; set; }
        public bool DeleteLocalAfterUpload { get; set; } = false;
        public int LocalRetentionDays { get; set; } = 7;
        public int CloudRetentionDays { get; set; } = 30;
        public int MaxLocalBackups { get; set; } = 10;
        public int MaxCloudBackups { get; set; } = 50;
        public bool NotifyOnSuccess { get; set; } = false;
        public bool NotifyOnFailure { get; set; } = true;
        public string? NotificationEmail { get; set; }
        public bool CompressBackups { get; set; } = true;
        public bool EncryptBackups { get; set; } = false;
        public bool VerifyIntegrity { get; set; } = true;
    }

    public class CloudStorageConfigDto
    {
        public int ConfigId { get; set; }
        public string ConfigName { get; set; } = string.Empty;
        public string ProviderName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }
}
