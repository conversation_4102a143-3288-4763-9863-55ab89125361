
using System;
using System.Collections.Generic;

#nullable enable

namespace AppDev.Server.Models
{
    public class DatabaseHealthReport
    {
        public string OverallHealth { get; set; } = "غير معروف";
        public bool ConnectionStatus { get; set; }
        public List<TableStatus> RequiredTables { get; set; } = new();
        public DatabaseStatistics Statistics { get; set; } = new();
        public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
    }

    public class TableStatus
    {
        public string Name { get; set; } = string.Empty;
        public bool Exists { get; set; }
        public long RecordCount { get; set; }
        public string Status => Exists ? "موجود" : "غير موجود";
    }

    public class DatabaseStatistics
    {
        public DateTime? LastBackup { get; set; }
        public int BackupCount { get; set; }
        public string LastBackupFormatted => LastBackup?.ToString("dd/MM/yyyy hh:mm tt", new System.Globalization.CultureInfo("en-US")) ?? "لا يوجد";
    }

    public class MigrationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class BackupResult
    {
        public bool Success { get; set; }
        public string? BackupPath { get; set; }
        public string? Message { get; set; }
    }

    public class BackupInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public long SizeBytes { get; set; }
        public string SizeFormatted => $"{SizeBytes / 1024.0:F2} KB";
    }

    public class ActivityLogEntry
    {
        public DateTime Timestamp { get; set; }
        public string Message { get; set; } = string.Empty;
        public LogType Type { get; set; }
    }

    public enum LogType
    {
        Info,
        Success,
        Warning,
        Error
    }

    public class RestoreResult
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public List<string> RestoredTables { get; set; } = new();
        public int RestoredRecords { get; set; }
    }

    public class BackupValidationResult
    {
        public bool IsValid { get; set; }
        public string? Message { get; set; }
        public DateTime? BackupDate { get; set; }
        public List<string> Tables { get; set; } = new();
        public string BackupType { get; set; } = string.Empty;
        public long SizeBytes { get; set; }
    }

    public class RestoreRequest
    {
        public string FileName { get; set; } = string.Empty;
        public bool ConfirmRestore { get; set; }
        public List<string> TablesToRestore { get; set; } = new();
    }
}
