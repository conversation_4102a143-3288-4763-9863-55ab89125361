using CG.Web.MegaApiClient;
using AppDev.Server.Data;
using AppDev.Server.Models;
using AppDev.Server.Models.AppDevDB;
using Microsoft.EntityFrameworkCore;

namespace AppDev.Server.Services
{
    public class MegaService : ICloudStorageService
    {
        private readonly AppDevDBContext _dbContext;
        private readonly ILogger<MegaService> _logger;

        public MegaService(AppDevDBContext dbContext, ILogger<MegaService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        public async Task<CloudStorageTestResult> TestConnectionAsync(int configId)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null)
                {
                    return new CloudStorageTestResult
                    {
                        Success = false,
                        Message = "إعدادات التخزين السحابي غير موجودة",
                        ErrorCode = "CONFIG_NOT_FOUND"
                    };
                }

                var megaClient = await CreateMegaClientAsync(config);
                if (megaClient == null)
                {
                    return new CloudStorageTestResult
                    {
                        Success = false,
                        Message = "فشل في إنشاء عميل Mega",
                        ErrorCode = "CLIENT_CREATION_FAILED"
                    };
                }

                // Test by getting account info
                var accountInfo = await megaClient.GetAccountInformationAsync();

                return new CloudStorageTestResult
                {
                    Success = true,
                    Message = "تم الاتصال بنجاح مع Mega",
                    Details = new Dictionary<string, object>
                    {
                        ["TotalQuota"] = accountInfo.TotalQuota.ToString(),
                        ["UsedQuota"] = accountInfo.UsedQuota.ToString()
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اختبار الاتصال مع Mega للإعدادات {ConfigId}", configId);
                return new CloudStorageTestResult
                {
                    Success = false,
                    Message = $"خطأ في الاتصال: {ex.Message}",
                    ErrorCode = "CONNECTION_ERROR"
                };
            }
        }

        public async Task<CloudUploadResult> UploadFileAsync(CloudUploadRequest request)
        {
            try
            {
                var config = await GetConfigAsync(request.ConfigId);
                if (config == null)
                {
                    return new CloudUploadResult
                    {
                        Success = false,
                        Message = "إعدادات التخزين السحابي غير موجودة",
                        ErrorCode = "CONFIG_NOT_FOUND"
                    };
                }

                var megaClient = await CreateMegaClientAsync(config);
                if (megaClient == null)
                {
                    return new CloudUploadResult
                    {
                        Success = false,
                        Message = "فشل في إنشاء عميل Mega",
                        ErrorCode = "CLIENT_CREATION_FAILED"
                    };
                }

                // Create upload record
                var uploadRecord = new AppBackupCloudUpload
                {
                    ConfigId = request.ConfigId,
                    ProviderId = config.ProviderId,
                    LocalFilePath = request.LocalFilePath,
                    LocalFileName = Path.GetFileName(request.LocalFilePath),
                    CloudFileName = request.CustomFileName ?? Path.GetFileName(request.LocalFilePath),
                    BackupType = request.BackupType,
                    FileSizeBytes = new FileInfo(request.LocalFilePath).Length,
                    UploadStatus = "InProgress",
                    UploadStartTime = DateTime.UtcNow,
                    Tags = request.Tags,
                    Notes = request.Notes,
                    CreatedDate = DateTime.UtcNow
                };

                _dbContext.AppBackupCloudUploads.Add(uploadRecord);
                await _dbContext.SaveChangesAsync();

                // Ensure folder exists
                var folderNode = await EnsureFolderExistsAsync(megaClient, config.FolderPath ?? "/AppDev/Backups");

                // Upload file
                using var stream = new FileStream(request.LocalFilePath, FileMode.Open, FileAccess.Read);
                var uploadedNode = await megaClient.UploadAsync(stream, uploadRecord.CloudFileName, folderNode);

                // Update upload record
                uploadRecord.CloudFileId = uploadedNode.Id;
                uploadRecord.CloudFilePath = GetNodePath(megaClient, uploadedNode);
                uploadRecord.UploadStatus = "Completed";
                uploadRecord.UploadEndTime = DateTime.UtcNow;
                uploadRecord.UploadDuration = uploadRecord.UploadEndTime - uploadRecord.UploadStartTime;
                uploadRecord.UploadProgress = 100;

                await _dbContext.SaveChangesAsync();

                // Delete local file if requested
                if (request.DeleteLocalAfterUpload && System.IO.File.Exists(request.LocalFilePath))
                {
                    System.IO.File.Delete(request.LocalFilePath);
                }

                var downloadLink = await megaClient.GetDownloadLinkAsync(uploadedNode);

                return new CloudUploadResult
                {
                    Success = true,
                    Message = "تم رفع الملف بنجاح إلى Mega",
                    UploadId = uploadRecord.UploadId,
                    CloudFileId = uploadedNode.Id,
                    CloudFileUrl = downloadLink?.ToString(),
                    FileSizeBytes = uploadedNode.Size
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في رفع الملف إلى Mega");
                return new CloudUploadResult
                {
                    Success = false,
                    Message = $"خطأ في رفع الملف: {ex.Message}",
                    ErrorCode = "UPLOAD_ERROR"
                };
            }
        }

        public async Task<bool> DeleteFileAsync(int configId, string cloudFileId)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return false;

                var megaClient = await CreateMegaClientAsync(config);
                if (megaClient == null) return false;

                var nodes = await megaClient.GetNodesAsync();
                var fileNode = nodes.FirstOrDefault(n => n.Id == cloudFileId);

                if (fileNode != null)
                {
                    await megaClient.DeleteAsync(fileNode, false);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الملف من Mega");
                return false;
            }
        }

        public async Task<CloudStorageQuotaInfo> GetQuotaInfoAsync(int configId)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return new CloudStorageQuotaInfo();

                var megaClient = await CreateMegaClientAsync(config);
                if (megaClient == null) return new CloudStorageQuotaInfo();

                var accountInfo = await megaClient.GetAccountInformationAsync();
                var total = accountInfo.TotalQuota;
                var used = accountInfo.UsedQuota;
                var available = total - used;

                return new CloudStorageQuotaInfo
                {
                    TotalBytes = total,
                    UsedBytes = used,
                    AvailableBytes = available,
                    UsagePercentage = total > 0 ? (double)used / total * 100 : 0,
                    FormattedTotal = FormatFileSize(total),
                    FormattedUsed = FormatFileSize(used),
                    FormattedAvailable = FormatFileSize(available)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معلومات المساحة من Mega");
                return new CloudStorageQuotaInfo();
            }
        }

        public async Task<List<CloudFileInfo>> ListFilesAsync(int configId, string? folderPath = null)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return new List<CloudFileInfo>();

                var megaClient = await CreateMegaClientAsync(config);
                if (megaClient == null) return new List<CloudFileInfo>();

                var folderNode = await GetFolderNodeAsync(megaClient, folderPath ?? config.FolderPath ?? "/AppDev/Backups");
                var nodes = await megaClient.GetNodesAsync(folderNode);

                return nodes.Where(n => n.Type == NodeType.File || n.Type == NodeType.Directory)
                    .Select(n => new CloudFileInfo
                    {
                        Id = n.Id,
                        Name = n.Name,
                        Size = n.Size,
                        CreatedDate = n.CreationDate ?? DateTime.MinValue,
                        ModifiedDate = n.ModificationDate ?? DateTime.MinValue,
                        IsFolder = n.Type == NodeType.Directory,
                        Path = GetNodePath(megaClient, n)
                    }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب قائمة الملفات من Mega");
                return new List<CloudFileInfo>();
            }
        }

        public async Task<Stream> DownloadFileAsync(int configId, string cloudFileId)
        {
            var config = await GetConfigAsync(configId);
            if (config == null) throw new InvalidOperationException("إعدادات التخزين السحابي غير موجودة");

            var megaClient = await CreateMegaClientAsync(config);
            if (megaClient == null) throw new InvalidOperationException("فشل في إنشاء عميل Mega");

            var nodes = await megaClient.GetNodesAsync();
            var fileNode = nodes.FirstOrDefault(n => n.Id == cloudFileId);

            if (fileNode == null) throw new FileNotFoundException("الملف غير موجود");

            return await megaClient.DownloadAsync(fileNode);
        }

        public async Task<bool> CreateFolderAsync(int configId, string folderPath)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return false;

                var megaClient = await CreateMegaClientAsync(config);
                if (megaClient == null) return false;

                await EnsureFolderExistsAsync(megaClient, folderPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء المجلد في Mega");
                return false;
            }
        }

        public async Task<string> GetShareLinkAsync(int configId, string cloudFileId)
        {
            try
            {
                var config = await GetConfigAsync(configId);
                if (config == null) return string.Empty;

                var megaClient = await CreateMegaClientAsync(config);
                if (megaClient == null) return string.Empty;

                var nodes = await megaClient.GetNodesAsync();
                var fileNode = nodes.FirstOrDefault(n => n.Id == cloudFileId);

                if (fileNode != null)
                {
                    var downloadLink = await megaClient.GetDownloadLinkAsync(fileNode);
                    return downloadLink?.ToString() ?? string.Empty;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على رابط المشاركة من Mega");
                return string.Empty;
            }
        }

        // Helper methods
        private async Task<AppCloudStorageConfig?> GetConfigAsync(int configId)
        {
            return await _dbContext.AppCloudStorageConfigs
                .Include(c => c.Provider)
                .FirstOrDefaultAsync(c => c.ConfigId == configId && c.IsActive);
        }

        private async Task<MegaApiClient?> CreateMegaClientAsync(AppCloudStorageConfig config)
        {
            try
            {
                if (string.IsNullOrEmpty(config.Username) || string.IsNullOrEmpty(config.EncryptedPassword))
                {
                    return null;
                }

                var megaClient = new MegaApiClient();
                await megaClient.LoginAsync(config.Username, DecryptPassword(config.EncryptedPassword));
                return megaClient;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء عميل Mega");
                return null;
            }
        }

        private async Task<INode> EnsureFolderExistsAsync(MegaApiClient megaClient, string folderPath)
        {
            var parts = folderPath.Trim('/').Split('/');
            var nodes = await megaClient.GetNodesAsync();
            var currentParent = nodes.FirstOrDefault(n => n.Type == NodeType.Root);

            foreach (var part in parts)
            {
                if (string.IsNullOrEmpty(part)) continue;

                var existingFolder = nodes.FirstOrDefault(n =>
                    n.Name == part &&
                    n.Type == NodeType.Directory &&
                    n.ParentId == currentParent?.Id);

                if (existingFolder != null)
                {
                    currentParent = existingFolder;
                }
                else
                {
                    currentParent = await megaClient.CreateFolderAsync(part, currentParent);
                    nodes = await megaClient.GetNodesAsync(); // Refresh nodes
                }
            }

            return currentParent ?? nodes.First(n => n.Type == NodeType.Root);
        }

        private async Task<INode> GetFolderNodeAsync(MegaApiClient megaClient, string folderPath)
        {
            if (string.IsNullOrEmpty(folderPath) || folderPath == "/")
            {
                var nodes = await megaClient.GetNodesAsync();
                return nodes.First(n => n.Type == NodeType.Root);
            }

            return await EnsureFolderExistsAsync(megaClient, folderPath);
        }

        private string GetNodePath(MegaApiClient megaClient, INode node)
        {
            // This is a simplified implementation
            // In a real scenario, you'd traverse up the parent hierarchy
            return $"/{node.Name}";
        }

        private string DecryptPassword(string encryptedPassword)
        {
            // TODO: Implement proper password decryption
            // For now, assume it's base64 encoded
            try
            {
                var bytes = Convert.FromBase64String(encryptedPassword);
                return System.Text.Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                return encryptedPassword; // Fallback to plain text
            }
        }

        private static string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
