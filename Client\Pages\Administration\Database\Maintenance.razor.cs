#nullable enable
using Microsoft.AspNetCore.Components;
using AppDev.Server.Models;
using Ra<PERSON>zen;
using System.Net.Http.Json;

namespace AppDev.Client.Pages.Administration.Database
{
    public partial class Maintenance : ComponentBase, IDisposable
    {
        [Inject] protected HttpClient HttpClient { get; set; } = default!;
        [Inject] protected NavigationManager NavigationManager { get; set; } = default!;
        [Inject] protected NotificationService NotificationService { get; set; } = default!;

        private DatabaseHealthReport? healthReport;
        private List<ActivityLogEntry> activityLog = new();
        private bool isLoading = false;
        private Timer? _healthCheckTimer;

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            await CheckDatabaseHealth();

            // بدء تحديث حالة الاتصال كل 30 ثانية
            _healthCheckTimer = new Timer(async _ => await CheckDatabaseHealthSilently(), null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        protected async Task CheckDatabaseHealth()
        {
            try
            {
                AddToLog("فحص حالة قاعدة البيانات...", LogType.Info);
                await LoadHealthCheck();
            }
            catch (Exception ex)
            {
                AddToLog($"خطأ في فحص حالة قاعدة البيانات: {ex.Message}", LogType.Error);
            }
        }

        private async Task CheckDatabaseHealthSilently()
        {
            try
            {
                await LoadHealthCheck();
            }
            catch
            {
                // تجاهل الأخطاء في الفحص الصامت
            }
        }

        private async Task LoadHealthCheck()
        {
            try
            {
                var baseUri = NavigationManager.BaseUri.TrimEnd('/');
                var response = await HttpClient.GetAsync($"{baseUri}/api/DatabaseMaintenance/health-check");

                if (response.IsSuccessStatusCode)
                {
                    healthReport = await response.Content.ReadFromJsonAsync<DatabaseHealthReport>();
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل تقرير الصحة: {ex.Message}");
            }
        }

        protected async Task ApplyMigrations()
        {
            isLoading = true;
            StateHasChanged();

            try
            {
                AddToLog("بدء تطبيق تحديثات قاعدة البيانات...", LogType.Info);

                var baseUri = NavigationManager.BaseUri.TrimEnd('/');
                var response = await HttpClient.PostAsync($"{baseUri}/api/DatabaseMaintenance/apply-migrations", null);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<MigrationResult>();
                    if (result?.Success == true)
                    {
                        AddToLog("تم تطبيق التحديثات بنجاح", LogType.Success);
                        ShowNotification("نجح", "تم تطبيق تحديثات قاعدة البيانات بنجاح", NotificationSeverity.Success);
                        await LoadHealthCheck();
                    }
                    else
                    {
                        AddToLog($"فشل في تطبيق التحديثات: {result?.Message}", LogType.Error);
                        ShowNotification("خطأ", result?.Message ?? "فشل في تطبيق التحديثات", NotificationSeverity.Error);
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    AddToLog($"خطأ في تطبيق التحديثات: {errorContent}", LogType.Error);
                    ShowNotification("خطأ", $"خطأ في تطبيق التحديثات: {response.StatusCode}", NotificationSeverity.Error);
                }
            }
            catch (Exception ex)
            {
                AddToLog($"خطأ في تطبيق التحديثات: {ex.Message}", LogType.Error);
                ShowNotification("خطأ", $"خطأ في تطبيق التحديثات: {ex.Message}", NotificationSeverity.Error);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        protected async Task OptimizeDatabase()
        {
            isLoading = true;
            StateHasChanged();

            try
            {
                AddToLog("بدء تحسين قاعدة البيانات...", LogType.Info);

                var baseUri = NavigationManager.BaseUri.TrimEnd('/');
                var response = await HttpClient.PostAsync($"{baseUri}/api/DatabaseMaintenance/optimize-database", null);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<dynamic>();
                    AddToLog("تم تحسين قاعدة البيانات بنجاح", LogType.Success);
                    ShowNotification("نجح", "تم تحسين قاعدة البيانات بنجاح", NotificationSeverity.Success);
                    await LoadHealthCheck();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    AddToLog($"خطأ في تحسين قاعدة البيانات: {errorContent}", LogType.Error);
                    ShowNotification("خطأ", "فشل في تحسين قاعدة البيانات", NotificationSeverity.Error);
                }
            }
            catch (Exception ex)
            {
                AddToLog($"خطأ في تحسين قاعدة البيانات: {ex.Message}", LogType.Error);
                ShowNotification("خطأ", $"خطأ في تحسين قاعدة البيانات: {ex.Message}", NotificationSeverity.Error);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        protected async Task RepairDatabase()
        {
            isLoading = true;
            StateHasChanged();

            try
            {
                AddToLog("بدء إصلاح قاعدة البيانات...", LogType.Info);

                var baseUri = NavigationManager.BaseUri.TrimEnd('/');
                var response = await HttpClient.PostAsync($"{baseUri}/api/DatabaseMaintenance/repair-database", null);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<dynamic>();
                    AddToLog("تم إصلاح قاعدة البيانات بنجاح", LogType.Success);
                    ShowNotification("نجح", "تم إصلاح قاعدة البيانات بنجاح", NotificationSeverity.Success);
                    await LoadHealthCheck();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    AddToLog($"خطأ في إصلاح قاعدة البيانات: {errorContent}", LogType.Error);
                    ShowNotification("خطأ", "فشل في إصلاح قاعدة البيانات", NotificationSeverity.Error);
                }
            }
            catch (Exception ex)
            {
                AddToLog($"خطأ في إصلاح قاعدة البيانات: {ex.Message}", LogType.Error);
                ShowNotification("خطأ", $"خطأ في إصلاح قاعدة البيانات: {ex.Message}", NotificationSeverity.Error);
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }

        protected void NavigateToBackup()
        {
            NavigationManager.NavigateTo("/admin/database/backup");
        }

        private void AddToLog(string message, LogType type)
        {
            activityLog.Add(new ActivityLogEntry
            {
                Message = message,
                Type = type,
                Timestamp = DateTime.Now
            });

            // الاحتفاظ بآخر 50 رسالة فقط
            if (activityLog.Count > 50)
            {
                activityLog = activityLog.TakeLast(50).ToList();
            }

            StateHasChanged();
        }

        private void ShowNotification(string title, string message, NotificationSeverity severity)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = severity,
                Summary = title,
                Detail = message,
                Duration = 4000
            });
        }

        private string GetHealthStatusColor(string health)
        {
            return health switch
            {
                "جيد" => "var(--rz-success)",
                "يحتاج صيانة" => "var(--rz-warning)",
                "سيء" => "var(--rz-danger)",
                _ => "var(--rz-text-secondary-color)"
            };
        }

        private string GetHealthStatusIcon(string health)
        {
            return health switch
            {
                "جيد" => "check_circle",
                "يحتاج صيانة" => "warning",
                "سيء" => "error",
                _ => "help"
            };
        }

        private BadgeStyle GetHealthBadgeStyle(string health)
        {
            return health switch
            {
                "جيد" => BadgeStyle.Success,
                "يحتاج صيانة" => BadgeStyle.Warning,
                "سيء" => BadgeStyle.Danger,
                _ => BadgeStyle.Secondary
            };
        }

        private AlertStyle GetLogAlertStyle(LogType type)
        {
            return type switch
            {
                LogType.Success => AlertStyle.Success,
                LogType.Warning => AlertStyle.Warning,
                LogType.Error => AlertStyle.Danger,
                _ => AlertStyle.Info
            };
        }

        public void Dispose()
        {
            _healthCheckTimer?.Dispose();
        }
    }


}
