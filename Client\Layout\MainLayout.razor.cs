using System.Net.Http;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Routing;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.Web.Virtualization;
using Microsoft.JSInterop;
using Radzen;
using Radzen.Blazor;

namespace AppDev.Client.Layout
{
    public partial class MainLayout
    {
        [Inject]
        protected IJSRuntime JSRuntime { get; set; }

        [Inject]
        protected NavigationManager NavigationManager { get; set; }

        [Inject]
        protected DialogService DialogService { get; set; }

        [Inject]
        protected TooltipService TooltipService { get; set; }

        [Inject]
        protected ContextMenuService ContextMenuService { get; set; }

        [Inject]
        protected NotificationService NotificationService { get; set; }

        [Inject]
        protected SecurityService Security { get; set; }

        private bool sidebarExpanded = true;
        private bool isDesktop = true;
        private string selectedUserAction = "";

        // User menu items
        private List<UserMenuItem> userMenuItems = new List<UserMenuItem>
        {
            new UserMenuItem { Text = "الملف الشخصي", Value = "profile" },
            new UserMenuItem { Text = "المستخدمون", Value = "users" },
            new UserMenuItem { Text = "الأدوار", Value = "roles" },
            new UserMenuItem { Text = "تسجيل الخروج", Value = "logout" }
        };

        protected override async Task OnInitializedAsync()
        {
            // Check if desktop
            isDesktop = await JSRuntime.InvokeAsync<bool>("window.matchMedia", "(min-width: 1024px)").AsTask();

            // Auto-collapse sidebar on mobile
            if (!isDesktop)
            {
                sidebarExpanded = false;
            }
        }

        void SidebarToggleClick()
        {
            sidebarExpanded = !sidebarExpanded;
        }

        void ToggleTheme()
        {
            // Theme toggle functionality can be implemented here
            // For now, just a placeholder
        }

        void OnUserMenuChange(object value)
        {
            var action = value?.ToString();
            switch (action)
            {
                case "profile":
                    NavigationManager.NavigateTo("/profile");
                    break;
                case "users":
                    NavigationManager.NavigateTo("/administration/users");
                    break;
                case "roles":
                    NavigationManager.NavigateTo("/administration/roles");
                    break;
                case "logout":
                    Security.Logout();
                    break;
            }
            selectedUserAction = ""; // Reset selection
        }

        protected void ProfileMenuClick(RadzenProfileMenuItem args)
        {
            if (args.Value == "Logout")
            {
                Security.Logout();
            }
        }

        private string IsActive(string path)
        {
            var currentPath = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);

            // Handle root path
            if (path == "/" && (currentPath == "" || currentPath == "/"))
            {
                return "active";
            }

            // Handle other paths
            if (path != "/" && currentPath.StartsWith(path.TrimStart('/'), StringComparison.OrdinalIgnoreCase))
            {
                return "active";
            }

            return "";
        }
    }

    public class UserMenuItem
    {
        public string Text { get; set; } = "";
        public string Value { get; set; } = "";
    }
}
