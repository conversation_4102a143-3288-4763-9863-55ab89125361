using System.ComponentModel.DataAnnotations;

namespace AppDev.Server.Models
{
    // DTOs for API responses
    public class CloudStorageProviderDto
    {
        public int ProviderId { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public string ProviderType { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public bool RequiresAuthentication { get; set; }
        public string? SupportedFileTypes { get; set; }
        public long? MaxFileSizeBytes { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class CloudStorageConfigDto
    {
        public int ConfigId { get; set; }
        public int ProviderId { get; set; }
        public string ConfigName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? FolderPath { get; set; }
        public string? FileNamePrefix { get; set; }
        public bool AutoUpload { get; set; }
        public bool DeleteLocalAfterUpload { get; set; }
        public int? RetentionDays { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public DateTime? LastSyncDate { get; set; }
        public string? LastSyncStatus { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public string ProviderType { get; set; } = string.Empty;
    }

    public class BackupCloudUploadDto
    {
        public int UploadId { get; set; }
        public string LocalFileName { get; set; } = string.Empty;
        public string CloudFileName { get; set; } = string.Empty;
        public string? CloudFileUrl { get; set; }
        public string BackupType { get; set; } = string.Empty;
        public long FileSizeBytes { get; set; }
        public string UploadStatus { get; set; } = string.Empty;
        public int UploadProgress { get; set; }
        public DateTime? UploadStartTime { get; set; }
        public DateTime? UploadEndTime { get; set; }
        public TimeSpan? UploadDuration { get; set; }
        public string? ErrorMessage { get; set; }
        public DateTime CreatedDate { get; set; }
        public string ProviderName { get; set; } = string.Empty;
        public string ConfigName { get; set; } = string.Empty;
    }

    // Request models for creating/updating configurations
    public class CreateCloudStorageConfigRequest
    {
        [Required]
        public int ProviderId { get; set; }

        [Required]
        [StringLength(100)]
        public string ConfigName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        // Authentication (will be encrypted)
        public string? ClientId { get; set; }
        public string? ClientSecret { get; set; }
        public string? ApiKey { get; set; }
        public string? Username { get; set; }
        public string? Password { get; set; }

        // Storage Settings
        [StringLength(500)]
        public string? FolderPath { get; set; } = "/AppDev/Backups";

        [StringLength(100)]
        public string? FileNamePrefix { get; set; } = "AppDev_";

        public bool AutoUpload { get; set; } = false;
        public bool DeleteLocalAfterUpload { get; set; } = false;
        public int? RetentionDays { get; set; } = 30;

        // Notification Settings
        public bool NotifyOnSuccess { get; set; } = true;
        public bool NotifyOnFailure { get; set; } = true;
        public string? NotificationEmail { get; set; }

        public bool IsDefault { get; set; } = false;
    }

    public class UpdateCloudStorageConfigRequest : CreateCloudStorageConfigRequest
    {
        [Required]
        public int ConfigId { get; set; }
    }

    public class CloudUploadRequest
    {
        [Required]
        public int ConfigId { get; set; }

        [Required]
        public string LocalFilePath { get; set; } = string.Empty;

        [Required]
        public string BackupType { get; set; } = string.Empty;

        public string? CustomFileName { get; set; }
        public string? Tags { get; set; }
        public string? Notes { get; set; }
        public bool DeleteLocalAfterUpload { get; set; } = false;
    }

    public class CloudUploadResult
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public int? UploadId { get; set; }
        public string? CloudFileId { get; set; }
        public string? CloudFileUrl { get; set; }
        public long? FileSizeBytes { get; set; }
        public string? ErrorCode { get; set; }
    }

    public class CloudStorageTestResult
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public string? ErrorCode { get; set; }
        public Dictionary<string, object>? Details { get; set; }
    }

    public class CloudStorageQuotaInfo
    {
        public long? TotalBytes { get; set; }
        public long? UsedBytes { get; set; }
        public long? AvailableBytes { get; set; }
        public double? UsagePercentage { get; set; }
        public string? FormattedTotal { get; set; }
        public string? FormattedUsed { get; set; }
        public string? FormattedAvailable { get; set; }
    }

    public class BackupScheduleDto
    {
        public int ScheduleId { get; set; }
        public string ScheduleName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string BackupType { get; set; } = string.Empty;
        public string ScheduleType { get; set; } = string.Empty;
        public string CronExpression { get; set; } = string.Empty;
        public bool UploadToCloud { get; set; }
        public string? CloudConfigName { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastRunTime { get; set; }
        public DateTime? NextRunTime { get; set; }
        public string? LastRunStatus { get; set; }
        public int SuccessfulRuns { get; set; }
        public int FailedRuns { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class CreateBackupScheduleRequest
    {
        [Required]
        [StringLength(100)]
        public string ScheduleName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [StringLength(50)]
        public string BackupType { get; set; } = "JSON";

        [Required]
        [StringLength(50)]
        public string ScheduleType { get; set; } = "Daily";

        [Required]
        [StringLength(100)]
        public string CronExpression { get; set; } = "0 2 * * *";

        public bool UploadToCloud { get; set; } = false;
        public int? CloudConfigId { get; set; }
        public bool DeleteLocalAfterUpload { get; set; } = false;

        public int LocalRetentionDays { get; set; } = 7;
        public int CloudRetentionDays { get; set; } = 30;
        public int MaxLocalBackups { get; set; } = 10;
        public int MaxCloudBackups { get; set; } = 50;

        public bool NotifyOnSuccess { get; set; } = false;
        public bool NotifyOnFailure { get; set; } = true;
        public string? NotificationEmail { get; set; }

        public bool CompressBackups { get; set; } = true;
        public bool EncryptBackups { get; set; } = false;
        public bool VerifyIntegrity { get; set; } = true;
    }

    public class UpdateBackupScheduleRequest : CreateBackupScheduleRequest
    {
        [Required]
        public int ScheduleId { get; set; }
    }

    // Enums
    public enum CloudProviderType
    {
        GoogleDrive,
        Mega,
        OneDrive,
        Dropbox,
        AmazonS3,
        Custom
    }

    public enum UploadStatus
    {
        Pending,
        InProgress,
        Completed,
        Failed,
        Cancelled,
        Retrying
    }

    public enum BackupType
    {
        JSON,
        SQL,
        Manual,
        Scheduled
    }

    public enum ScheduleType
    {
        Daily,
        Weekly,
        Monthly,
        Custom
    }

    // Additional request models for backup operations
    public class UploadExistingBackupRequest
    {
        [Required]
        public string FileName { get; set; } = string.Empty;

        [Required]
        public int ConfigId { get; set; }

        public string? BackupType { get; set; }
        public string? CustomFileName { get; set; }
        public string? Tags { get; set; }
        public string? Notes { get; set; }
        public bool DeleteLocalAfterUpload { get; set; } = false;
    }

    public class CreateBackupAndUploadRequest
    {
        public string? BackupType { get; set; } = "JSON"; // JSON or SQL
        public bool? IncludeData { get; set; } = true; // For SQL backups
        public bool? CompressBackup { get; set; } = true;
        public int? CloudConfigId { get; set; }
        public string? CustomFileName { get; set; }
        public string? Tags { get; set; }
        public string? Notes { get; set; }
        public bool? DeleteLocalAfterUpload { get; set; } = false;
    }

    // Dashboard Data Models
    public class BackupDashboardData
    {
        public int LocalBackupsCount { get; set; }
        public int CloudBackupsCount { get; set; }
        public int ActiveSchedulesCount { get; set; }
        public int ActiveCloudConfigsCount { get; set; }
        public List<RecentBackupDto> RecentBackups { get; set; } = new();
        public List<ScheduleStatusDto> ScheduleStatus { get; set; } = new();
    }

    public class RecentBackupDto
    {
        public string FileName { get; set; } = string.Empty;
        public string BackupType { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public double FileSizeGB { get; set; }
    }

    public class ScheduleStatusDto
    {
        public string ScheduleName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime? LastRunTime { get; set; }
        public DateTime? NextRunTime { get; set; }
    }
}
