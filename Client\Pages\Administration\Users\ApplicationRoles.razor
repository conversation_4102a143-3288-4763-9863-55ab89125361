@page "/application-roles"
@page "/administration/roles"
@inject NotificationService NotificationService
@inject NavigationManager Navigation

<PageTitle>إدارة الأدوار</PageTitle>

<RadzenStack Gap="1rem">
    <RadzenRow>
        <RadzenColumn Size="12">
            <RadzenText TextStyle="TextStyle.H3" TagName="TagName.H1" class="rz-pt-8">
                <RadzenIcon Icon="admin_panel_settings" class="rz-me-1" />
                إدارة الأدوار
            </RadzenText>
        </RadzenColumn>
    </RadzenRow>

    <!-- Statistics Cards -->
    <RadzenRow Gap="1rem">
        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4"
                Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="admin_panel_settings" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">0</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">إجمالي الأدوار</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4"
                Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="star" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">5</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">الأدوار الافتراضية</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4"
                Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="group" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">0</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">المستخدمون المرتبطون</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="3">
            <RadzenCard class="rz-p-4"
                Style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="1rem">
                    <RadzenIcon Icon="add" Style="font-size: 2rem;" />
                    <RadzenStack Gap="0.25rem">
                        <RadzenText TextStyle="TextStyle.H4" class="rz-mb-0">0</RadzenText>
                        <RadzenText TextStyle="TextStyle.Caption">أدوار مخصصة</RadzenText>
                    </RadzenStack>
                </RadzenStack>
            </RadzenCard>
        </RadzenColumn>
    </RadzenRow>

    <!-- Action Buttons -->
    <RadzenCard class="rz-p-4">
        <RadzenStack Orientation="Orientation.Horizontal" Gap="1rem" Wrap="FlexWrap.Wrap">
            <RadzenButton Text="إضافة دور جديد" Icon="add" ButtonStyle="ButtonStyle.Primary" Click="@CreateNewRole" />
            <RadzenButton Text="الأدوار الافتراضية" Icon="star" ButtonStyle="ButtonStyle.Secondary"
                Click="@ShowDefaultRoles" />
            <RadzenButton Text="إدارة المستخدمين" Icon="group" ButtonStyle="ButtonStyle.Info"
                Click="@NavigateToUsers" />
        </RadzenStack>
    </RadzenCard>

    <!-- Coming Soon Message -->
    <RadzenCard class="rz-p-4 rz-text-align-center">
        <RadzenIcon Icon="construction" Style="font-size: 4rem; color: var(--rz-warning);" />
        <RadzenText TextStyle="TextStyle.H5" class="rz-mt-3">نظام إدارة الأدوار قيد التطوير</RadzenText>
        <RadzenText TextStyle="TextStyle.Body1" class="rz-mt-2">
            سيتم إضافة جميع ميزات إدارة الأدوار والصلاحيات قريباً
        </RadzenText>
    </RadzenCard>
</RadzenStack>

@code {

    private async Task CreateNewRole()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "معلومات",
            Detail = "ميزة إضافة الأدوار ستكون متاحة قريباً"
        });
    }

    private async Task ShowDefaultRoles()
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Info,
            Summary = "الأدوار الافتراضية",
            Detail = "Administrator, ReportManager, ReportDesigner, ReportViewer, User"
        });
    }

    private async Task NavigateToUsers()
    {
        Navigation.NavigateTo("/application-users");
    }
}
