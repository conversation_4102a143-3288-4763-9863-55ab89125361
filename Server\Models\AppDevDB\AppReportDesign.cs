using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AppDev.Server.Models.AppDevDB
{
    [Table("App_ReportDesigns")]
    public partial class AppReportDesign
    {

        [NotMapped]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("@odata.etag")]
        public string ETag
        {
            get;
            set;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int DesignId { get; set; }

        [ConcurrencyCheck]
        public int? PrintSettingId { get; set; }

        public AppReportPrintSetting PrintSetting { get; set; }

        [Required]
        [ConcurrencyCheck]
        public string DesignName { get; set; }

        [ConcurrencyCheck]
        public byte[] ReportDefinition { get; set; }

        [ConcurrencyCheck]
        public string ReportPath { get; set; }

        [Required]
        [ConcurrencyCheck]
        public DateTime DesignCreatedDate { get; set; }

        [ConcurrencyCheck]
        public DateTime? DesignLastModified { get; set; }

        [Required]
        [ConcurrencyCheck]
        public string DesignVersion { get; set; }

        [ConcurrencyCheck]
        public bool DesignIsActive { get; set; }

        [ConcurrencyCheck]
        public int? ReportId { get; set; }

        public AppReport Report { get; set; }

        [ConcurrencyCheck]
        public string CreatedByUserId { get; set; }

        [ConcurrencyCheck]
        public string LastModifiedByUserId { get; set; }
    }
}
