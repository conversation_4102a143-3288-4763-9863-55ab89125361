@inject NavigationManager NavigationManager
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="@NavigationManager.BaseUri" />
    <RadzenTheme @rendermode="@InteractiveAuto" Theme="material" />
    <link rel="stylesheet" href="css/site.css" />
    <link rel="icon" href="favicon.ico" />
    <link href="manifest.json" rel="manifest" />
    <link rel="apple-touch-icon" sizes="512x512" href="icon-512.png" />
    <HeadOutlet @rendermode="@InteractiveAuto" />
</head>

<body>
    <Routes @rendermode="@InteractiveAuto" />
    <script src="_framework/blazor.web.js"></script>
    <script src="_content/Radzen.Blazor/Radzen.Blazor.js?v=@(typeof(Radzen.Colors).Assembly.GetName().Version)"></script>
    <script>navigator.serviceWorker.register('service-worker.js');</script>
</body>

</html>

@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; }

    [Inject]
    private ThemeService ThemeService { get; set; }

    protected override void OnInitialized()
    {
        base.OnInitialized();

        if (HttpContext != null)
        {
            var theme = HttpContext.Request.Cookies["AppDevTheme"];

            if (!string.IsNullOrEmpty(theme))
            {
                ThemeService.SetTheme(theme, false);
            }
        }
    }
}
