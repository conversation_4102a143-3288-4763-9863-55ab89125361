using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace AppDev.Server.Models.AppDevDB
{
    [Table("App_ReportPrintSettings")]
    public partial class AppReportPrintSetting
    {

        [NotMapped]
        [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
        [JsonPropertyName("@odata.etag")]
        public string ETag
        {
            get;
            set;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int PrintSettingId { get; set; }

        [Required]
        [ConcurrencyCheck]
        public string SettingName { get; set; }

        [ConcurrencyCheck]
        public string PaperSize { get; set; }

        [ConcurrencyCheck]
        public string Orientation { get; set; }

        [ConcurrencyCheck]
        public decimal? MarginTop { get; set; }

        [ConcurrencyCheck]
        public decimal? MarginBottom { get; set; }

        [ConcurrencyCheck]
        public decimal? MarginLeft { get; set; }

        [ConcurrencyCheck]
        public decimal? MarginRight { get; set; }

        [ConcurrencyCheck]
        public bool? PrintHeader { get; set; }

        [ConcurrencyCheck]
        public bool? PrintFooter { get; set; }

        [ConcurrencyCheck]
        public string HeaderText { get; set; }

        [ConcurrencyCheck]
        public string FooterText { get; set; }

        [Required]
        [ConcurrencyCheck]
        public DateTime SettingCreatedDate { get; set; }

        [ConcurrencyCheck]
        public DateTime? SettingLastModified { get; set; }

        [ConcurrencyCheck]
        public bool IsActive { get; set; }

        [ConcurrencyCheck]
        public string CreatedByUserId { get; set; }

        [ConcurrencyCheck]
        public string LastModifiedByUserId { get; set; }

        public ICollection<AppReportDesign> AppReportDesigns { get; set; }
    }
}
