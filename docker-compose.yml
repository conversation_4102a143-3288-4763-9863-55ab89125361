
services:
  mysql:
    image: mysql:8.0
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: "*********"
      MYSQL_DATABASE: "AppDevDB"
      MYSQL_USER: "flydev"
      MYSQL_PASSWORD: "*********"
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      appdev_network:
        ipv4_address: **********0

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    restart: always
    depends_on:
      - mysql
    environment:
      PMA_HOST: "**********0" # استخدام IP الثابت لـ mysql
      PMA_PORT: "3306"
      PMA_ARBITRARY: 1
      PMA_SERVER_NAME: "phpmyadmin"
    ports:
      - "8081:80"
    networks:
      appdev_network:
        ipv4_address: ***********

  appdev-server:
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      - mysql
    environment:
      ASPNETCORE_ENVIRONMENT: "Development"
      ASPNETCORE_URLS: "http://*:8080"
      ConnectionStrings__DefaultConnection: "Server=**********0;Port=3306;Database=AppDevDB;User=flydev;Password=*********;" # استخدام IP الثابت لـ mysql
    ports:
      - "5000:8080"
    restart: on-failure
    volumes:
      - dataprotection_keys:/app/DataProtection-Keys
    networks:
      appdev_network:
        ipv4_address: ***********

volumes:
  mysql_data:
  dataprotection_keys:

networks:
  appdev_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
          gateway: **********
