<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <NoWarn>CS0168,CS1998,BL9993,<PERSON>0649,CS0436,0436,<PERSON>8632,<PERSON>8629,CS8600,<PERSON>86<PERSON>,CS8602,<PERSON>8603,<PERSON>8604,<PERSON>8618,CS8625,CS8981,<PERSON>8714,ASP0019,NU1608,NU1701,<PERSON>B3026,<PERSON>B3027,<PERSON>B3021,MSB3061</NoWarn>
    <NuGetAuditMode>direct</NuGetAuditMode>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />

    <!-- إعدادات لتجنب مشاكل ملفات .pdb -->
    <CopyDebugSymbolFilesFromPackages>false</CopyDebugSymbolFilesFromPackages>
    <CopyDocumentationFilesFromPackages>false</CopyDocumentationFilesFromPackages>
    <UseSharedCompilation>false</UseSharedCompilation>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.70.0.3856" />
    <PackageReference Include="MegaApiClient" Version="1.10.4" />
    <PackageReference Include="MySqlConnector" Version="2.4.0" />
    <PackageReference Include="Radzen.Blazor" Version="*" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.0" />
    <PackageReference Include="SharpZipLib" Version="1.4.2" />
    <ProjectReference Include="..\Client\AppDev.Client.csproj" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.20.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.0-beta.2" />
    <PackageReference Include="Microsoft.AspNetCore.OData" Version="9.*-*" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.HeaderPropagation" Version="8.0.0" />

    <!-- Stimulsoft Reports -->
    <PackageReference Include="Stimulsoft.Reports.Blazor" Version="2025.3.3" />
    <PackageReference Include="Stimulsoft.Reports.Web" Version="2025.3.3" />

    <!-- Microsoft CodeAnalysis - Latest compatible versions -->
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.8.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.8.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.8.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Workspaces.Common" Version="4.8.0" />

    <!-- مكتبات النسخ الاحتياطي الأساسية -->
    <PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
  </ItemGroup>

  <!-- استيراد ملف الحلول المخصص -->
  <Import Project="Server.targets" />

</Project>